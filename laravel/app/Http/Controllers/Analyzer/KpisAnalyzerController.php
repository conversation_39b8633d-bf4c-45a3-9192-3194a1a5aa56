<?php

namespace App\Http\Controllers\Analyzer;

use App\ActualVisit;
use App\CallRate;
use App\ClassFrequency;
use App\DivisionType;
use App\DoctorFrequency;
use App\Exceptions\CrmException;
use App\Http\Controllers\Controller;
use App\Line;
use App\LineDivision;
use App\Models\OffDay;
use App\Models\OtherSetting;
use App\OwActualVisit;
use App\Services\AccountService;
use App\Services\ActualService;
use App\Services\Analyzers\AnalyzerCallRateService;
use App\Services\Analyzers\AnalyzerCoverageService;
use App\Services\Analyzers\AnalyzerFrequencyService;
use App\Services\DoctorService;
use App\Services\Enums\AnalyzerCallRate;
use App\Services\Enums\AnalyzerCoverage;
use App\Services\Enums\AnalyzerFrequency;
use App\SpecialityFrequency;
use App\User;
use App\Vacation;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class KpisAnalyzerController extends Controller
{
    private AnalyzerCoverageService $coverageAnalyzer;
    private AnalyzerCallRateService $callRateAnalyzer;
    private AnalyzerFrequencyService $analyzerFrequencyService;

    // employee and division
    private bool $isFilterByEmployee = false;

    // doctor and account
    private bool $isFilterByDoctorType = false;
    private Carbon $from;
    private Carbon $to;
    private int $divisionType;
    // visit filter
    private array $visitFilter;
    private bool $isClassicFrequency;
    private array $months;
    private string $year;

    public function __construct()
    {
        $this->coverageAnalyzer = new AnalyzerCoverageService();
        $this->callRateAnalyzer = new AnalyzerCallRateService();
        $this->analyzerFrequencyService = new AnalyzerFrequencyService();
        // Don't set user in constructor for Octane compatibility
        // Will be set in each method that needs it
    }

    private function setVisitFilters()
    {
        $this->isFilterByEmployee = $this->visitFilter['filter'] == 2;
        $this->isFilterByDoctorType = $this->visitFilter['type'] == 2;
    }

    private function checkClassicFrequency()
    {
        $this->isClassicFrequency = ClassFrequency::select([
            'id',
        ])
            ->when(
                !empty($this->visitFilter['lines']),
                fn($q) => $q->whereIntegerInRaw('line_id', $this->visitFilter['lines'])
            )
            ->whereBetween(
                DB::raw("(TRIM(LEADING '0' FROM DATE_FORMAT(crm_class_frequencies.date,'%m')))"),
                $this->months
            )
            ->whereYear('class_frequencies.date', $this->year)->exists();
    }

    private function setDateBoundaries()
    {
        $this->from = Carbon::parse($this->visitFilter['fromDate'])->startOfDay();
        $this->to = Carbon::parse($this->visitFilter['toDate'])->endOfDay();

        $this->months = [(int)$this->from->format('m'), (int)$this->to->format('m')];
        $this->year = (int)$this->from->format('Y');
    }

    private function getDivisionsFilteredByDivisions($lines)
    {
        $filtered = collect();
        foreach ($lines as $line) {
            $divisions = $line->divisions($this->from, $this->to)
                ->when(!empty($this->visitFilter['divisions']), fn($q) => $q->whereIntegerInRaw("line_divisions.id", $this->visitFilter['divisions']))->get();
            $filtered = $filtered->merge(Auth::user()->filterDivisions($line, $divisions, $this->visitFilter, $this->from, $this->to));
        }
        return $filtered;
    }

    private function getUsersFilteredByEmployees($lines)
    {
        $filtered = collect();
        foreach ($lines as $line) {
            $users = $line->users($this->from, $this->to)
                ->when(!empty($this->visitFilter['users']), fn($q) => $q->whereIntegerInRaw("line_users.user_id", $this->visitFilter['users']))->get();
            $filtered = $filtered->merge(Auth::user()->filterUsers($line, $users, $this->visitFilter, $this->from, $this->to));
        }
        return $filtered;
    }

    public function filter(Request $request)
    {
        $this->visitFilter = $request->visitFilter;
        $this->setVisitFilters();
        $this->setDateBoundaries();
        $this->checkClassicFrequency();

        $lines = Line::when(
            !empty($this->visitFilter['lines']),
            fn($q) => $q->whereIntegerInRaw("lines.id", $this->visitFilter['lines'])
        )->with('classes')->get();

        //        $classes =

        $this->divisionType = DivisionType::where('last_level', '=', 1)->value('id');

        $this->isFilterByEmployee
            ? $this->whenFilterByEmployee($lines)
            : $this->whenFilterByDivision($lines);
        return response()->json([
            'coverage' => $this->isFilterByDoctorType
                ? $this->coverageAnalyzer->getDoctorCoverage()
                : $this->coverageAnalyzer->getAccountCoverage(),
            'call_rate' => $this->callRateAnalyzer->all(),
            'advancedFrequency' => $this->analyzerFrequencyService->all(),
            'classicFrequency' => $this->analyzerFrequencyService->all(),
        ]);
    }

    private function whenFilterByEmployee($lines): void
    {
        $data = collect([]);
        $this->getUsersFilteredByEmployees($lines)->each(function ($user) use (&$data) {
            [$lines, $divisionHolders] = $this->getLinesBelowDivisionsBelowUsers($user);
            $data = $data->push($divisionHolders);
        });
        $data = $data->collapse()->values();
        $divIds = $data->pluck('div_id')->unique()->values()->toArray();
        $usersIds = $data->pluck('user_id')->unique()->values()->toArray();
        $lineIds = $lines->pluck('id')->toArray();
        $doctorsAndAccounts = $this->countAccountsAndDoctors($lineIds, $divIds);
        $this->coveredAccountsAndDoctors($lineIds, $doctorsAndAccounts, $usersIds, 'user_id');
        $this->calculateCallRate($usersIds, $doctorsAndAccounts, $lineIds, 'users.id');
        $data->each(
            function ($divisionHolder) use ($doctorsAndAccounts, $lineIds) {
                $this->calculateFrequency(
                    $doctorsAndAccounts['doctors'],
                    $divisionHolder->user_id,
                    'users.id'
                );
            }
        );
    }

    private function whenFilterByDivision($lines): void
    {
        $data = collect([]);
        $this->getDivisionsFilteredByDivisions($lines)->each(function ($division) use (&$data) {
            [$lines, $divisionHolders] = $this->getLinesBelowDivisions($division);
            $data = $data->push($divisionHolders);
        });
        $data = $data->collapse()->values();
        $divIds = $data->pluck('id')->unique()->values()->toArray();
        $lineIds = $lines->pluck('id')->toArray();
        $doctorsAndAccounts = $this->countAccountsAndDoctors($lineIds, $divIds);
        $this->coveredAccountsAndDoctors($lineIds, $doctorsAndAccounts, $divIds, 'div_id');
        $this->calculateCallRate($divIds, $doctorsAndAccounts, $lineIds, 'users.id');
        $data->each(
            function ($divisionHolder) use ($doctorsAndAccounts, $lineIds) {
                $this->calculateFrequency(
                    $doctorsAndAccounts['doctors'],
                    $divisionHolder->id,
                    'line_divisions.id'
                );
            }
        );
    }

    private function getLinesBelowDivisions(LineDivision $division): array
    {
        $lines = $division->line()->get()->pluck('id')->toArray();
        $divisions = $division->getBelowDivisions(from: $this->from, to: $this->to)
            ->where('division_type_id', $this->divisionType)
            ->unique('div_id');

        return [$lines, $divisions];
    }

    private function getLinesBelowDivisionsBelowUsers(User $user): array
    {
        $lines = $user->lines->pluck('id')->toArray();
        $divisions = $user->allBelowUsersWithDivision(from: $this->from, to: $this->to)
            ->where('div_type_id', $this->divisionType)
            ->unique('user_id');
        return [$lines, $divisions];
    }


    /**
     * @throws \Exception
     */
    private function countAccountsAndDoctors(array $lines, array $divisions): array
    {

        $doctors = $this->isFilterByDoctorType
            ? (new DoctorService)->getDoctorsWithFrequency(
                lines: $lines,
                divisions: $divisions,
                from: $this->from,
                to: $this->to,
                months: [$this->from->format('m'), $this->to->format('m')],
                year: $this->from->format('Y'),
                specialities: $this->visitFilter['specialities'],
                accountTypes: $this->visitFilter['accountTypes'],
            )
            : collect();

        $accounts = !$this->isFilterByDoctorType
            ? (new AccountService)->getAccountsCoverage(
                lines: $lines,
                divisions: $divisions,
                from: $this->from,
                to: $this->to,
                specialities: $this->visitFilter['specialities'],
                accountTypes: $this->visitFilter['accountTypes']
            )
            : collect();
        $countPhDoctors = $doctors->where("acc_shift_id", 3)->count();
        $countAmDoctors = $doctors->where("acc_shift_id", 1)->count();
        $countPmDoctors = $doctors->where("acc_shift_id", 2)->count();
        $countPhAccounts = $accounts->where("acc_shift_id", 3)->count();
        $countAmAccounts = $accounts->where("acc_shift_id", 1)->count();
        $countPmAccounts = $accounts->where("acc_shift_id", 2)->count();


        $this->coverageAnalyzer->addTo(
            AnalyzerCoverage::PH_ACCOUNT_COVERAGE,
            0,
            $countPhAccounts
        );
        $this->coverageAnalyzer->addTo(
            AnalyzerCoverage::AM_ACCOUNT_COVERAGE,
            0,
            $countAmAccounts
        );
        $this->coverageAnalyzer->addTo(
            AnalyzerCoverage::PM_ACCOUNT_COVERAGE,
            0,
            $countPmAccounts
        );
        $this->coverageAnalyzer->addTo(
            AnalyzerCoverage::PH_DOCTOR_COVERAGE,
            0,
            $countPhDoctors
        );
        $this->coverageAnalyzer->addTo(
            AnalyzerCoverage::AM_DOCTOR_COVERAGE,
            0,
            $countAmDoctors
        );
        $this->coverageAnalyzer->addTo(
            AnalyzerCoverage::PM_DOCTOR_COVERAGE,
            0,
            $countPmDoctors
        );

        return [
            'accounts' => $accounts,
            'doctors' => $doctors,
        ];
    }


    private function getVisitsFilteredByDoctors(
        array      $belowDivisions,
        array      $lines,
        Collection $doctors,
        string     $filterBy
    ) {
        $visits = ActualVisit::whereIntegerInRaw('line_id', $lines)
            ->whereIn($filterBy, $belowDivisions)
            ->whereIntegerInRaw('account_dr_id', $doctors->pluck('doctor_id'))
            ->whereBetween('visit_date', [$this->from, $this->to])
            ->get()->unique('account_dr_id')->count();
        return $visits;
    }

    private function getVisitsFilteredByAccounts(
        array      $belowDivisions,
        array      $lines,
        Collection $accounts,
        string     $filterBy
    ) {
        $visits = ActualVisit::whereIntegerInRaw('line_id', $lines)
            ->whereIn($filterBy, $belowDivisions)
            ->whereIntegerInRaw('account_id', $accounts->pluck('account_id'))
            ->whereBetween('visit_date', [$this->from, $this->to])
            ->get()->unique('account_id')->count();
        return $visits;
    }

    /**
     * @throws \Exception
     */
    public function coveredAccountsAndDoctors(array $lines, array $count, array $Ids, string $filterBy): void
    {
        if ($this->isFilterByDoctorType) {
            $phDoctors = $count['doctors']->where("acc_shift_id", 3);
            $amDoctors = $count['doctors']->where("acc_shift_id", 1);
            $pmDoctors = $count['doctors']->where("acc_shift_id", 2);
            $phCoverage = $this->getVisitsFilteredByDoctors($Ids, $lines, $phDoctors, $filterBy);
            $amCoverage = $this->getVisitsFilteredByDoctors($Ids, $lines, $amDoctors, $filterBy);
            $pmCoverage = $this->getVisitsFilteredByDoctors($Ids, $lines, $pmDoctors, $filterBy);

            $this->coverageAnalyzer->addTo(AnalyzerCoverage::PH_DOCTOR_COVERAGE, $phCoverage, 0);
            $this->coverageAnalyzer->addTo(AnalyzerCoverage::AM_DOCTOR_COVERAGE, $amCoverage, 0);
            $this->coverageAnalyzer->addTo(AnalyzerCoverage::PM_DOCTOR_COVERAGE, $pmCoverage, 0);
            return;
        }

        $phAccounts = $count['accounts']->where("acc_shift_id", 3);
        $amAccounts = $count['accounts']->where("acc_shift_id", 1);
        $pmAccounts = $count['accounts']->where("acc_shift_id", 2);

        $phCoverage = $this->getVisitsFilteredByAccounts($Ids, $lines, $phAccounts, $filterBy);
        $amCoverage = $this->getVisitsFilteredByAccounts($Ids, $lines, $amAccounts, $filterBy);
        $pmCoverage = $this->getVisitsFilteredByAccounts($Ids, $lines, $pmAccounts, $filterBy);

        $this->coverageAnalyzer->addTo(AnalyzerCoverage::PH_ACCOUNT_COVERAGE, $phCoverage, 0);
        $this->coverageAnalyzer->addTo(AnalyzerCoverage::AM_ACCOUNT_COVERAGE, $amCoverage, 0);
        $this->coverageAnalyzer->addTo(AnalyzerCoverage::PM_ACCOUNT_COVERAGE, $pmCoverage, 0);
    }

    private function calculateCallRate($Ids, $count, array $lines, string $filterBy)
    {
        // $user = new User();
        // $user->id = $holder->user_id;
        $doctorIds = $count['doctors']->pluck('doctor_id')->toArray();
        $actuals = (new ActualService)->teamActuals(
            from: $this->from,
            to: $this->to,
            users: $Ids,
            table: $filterBy,
            lines: $lines,
            doctorIds: $doctorIds,
        );
        if ($this->isFilterByDoctorType) {
            $phActuals = $actuals->where('acc_shift_id', 3);
            $amActuals = $actuals->where('acc_shift_id', 1);
            $pmActuals = $actuals->where('acc_shift_id', 2);
            $amActualDays = $amActuals->pluck('date')->unique()->count();
            $pmActualDays = $pmActuals->pluck('date')->unique()->count();
            $phActualDays = $phActuals->pluck('date')->unique()->count();

            $phCallRate = ($phActualDays) ? round($phActuals->count() / $phActualDays, 0) : 0;
            $pmCallRate = ($pmActualDays) ? round($pmActuals->count() / $pmActualDays, 0) : 0;
            $amCallRate = ($amActualDays) ? round($amActuals->count() / $amActualDays, 0) : 0;
            // throw new CrmException([$pmCallRate,$amCallRate,$phCallRate]);

            $this->callRateAnalyzer->addTo(AnalyzerCallRate::PH_Actual_CALLRATE, $phCallRate, $phActuals->count());
            $this->callRateAnalyzer->addTo(AnalyzerCallRate::AM_Actual_CALLRATE, $amCallRate, $amActuals->count());
            $this->callRateAnalyzer->addTo(AnalyzerCallRate::PM_Actual_CALLRATE, $pmCallRate, $pmActuals->count());
            return;
        }
        // $divisions = $user->allBelowDivisions()->whereIn('line_id', $lines)->where('is_kol', 0)
        //     ->where('division_type_id', $this->divisionType)->unique('id')->pluck('id')->toArray();
        // $doctors = (new DoctorService)->getDoctorsِCoverage(
        //     lines: $lines,
        //     divisions: $divisions,
        //     from: $this->from,
        //     to: $this->to,
        //     specialities: $this->visitFilter['specialities'],
        //     accountTypes: $this->visitFilter['accountTypes'],
        // );
        // $countPhDoctors = $doctors->where("acc_shift_id", 3)->count();
        // $countAmDoctors = $doctors->where("acc_shift_id", 1)->count();
        // $countPmDoctors = $doctors->where("acc_shift_id", 2)->count();

        // $actuals = (new ActualService)->getActuals(
        //     $user,
        //     $filterBy,
        //     $this->from,
        //     $this->to,
        //     [$shift],
        //     $lines,
        //     [],
        //     [],
        //     null,
        //     $doctorIds
        // );
        // $actualShifts = $actuals->where('acc_shift_id', $shift);
        // $actualDays = $actualShifts->pluck('date')->unique()->count();
        // $actualCounts = $actualShifts->count();
        // return ($actualDays) ? round($actualCounts / $actualDays, 0) : 0;
        // $vacations = $this->getVacationCount($holder->user_id, $shift);
        // $workingDays = CallRate::workingDays($this->from, $this->to, $shift);
        // $officeWorkDays = $this->getOfficeWorkDays($holder->user_id, $shift);
        // $net_days = $workingDays - $vacations - $officeWorkDays;
        // $dailyTarget = $this->getDailyTarget($holder->div_id, $shift);
        // $monthlyTarget = $dailyTarget * $net_days;

        // return ($monthlyTarget ? $actualCounts / $monthlyTarget : $monthlyTarget) * 100;
    }

    // function calculateCallRate($holder, $doctorsAccounts, array $lines): void
    // {
    //     $filterBy = "users.id";
    //     $amCallRate = $this->calculate($holder, $doctorsAccounts, $lines, 1, $filterBy);
    //     $pmCallRate = $this->calculate($holder, $doctorsAccounts, $lines, 2, $filterBy);
    //     $phCallRate = $this->calculate($holder, $doctorsAccounts, $lines, 3, $filterBy);
    //     $this->callRateAnalyzer->addTo(AnalyzerCallRate::AM_CALL_RATE, $amCallRate);
    //     $this->callRateAnalyzer->addTo(AnalyzerCallRate::PM_CALL_RATE, $pmCallRate);
    //     $this->callRateAnalyzer->addTo(AnalyzerCallRate::PH_CALL_RATE, $phCallRate);
    // }


    private function getVacationCount(int $userId, $shift): int
    {
        $user = new User();
        $user->id = $userId;
        $months = [$this->from->format('m'), $this->to->format('m')];
        $year = $this->from->format('Y');
        return Vacation::getVacationsDatePerPeriod(
            $user,
            $this->from,
            $this->to,
            $months,
            $year,
            [$shift]
        );
    }

    private function getOfficeWorkDays(int $userId, int $shift): int
    {
        $ow = OwActualVisit::where('user_id', $userId)->whereBetween('date', [$this->from, $this->to]);
        $ow = $ow->where(fn($q) => $q->where('shift_id', $shift)->orWhereNull('shift_id'));

        return $ow->get()->filter(function ($ow) use ($shift) {
            return !OffDay::isOffDay($this->from, $this->to, $ow->date, $shift);
        })->count();
    }

    private function getDailyTarget($divisionId, $shift)
    {
        $months = [
            $this->from->format('m'),
            $this->to->format('m')
        ];
        $year = $this->from->format('Y');

        $callRate = CallRate::where('division_id', $divisionId)
            ->whereBetween(DB::raw("(DATE_FORMAT(crm_call_rates.date,'%m'))"), $months)
            ->whereYear('call_rates.date', $year);

        $callRate = $callRate->where('shift_id', $shift);

        return $callRate->first()?->call_rate ?? 0;
    }

    private function calculateFrequency(Collection $doctors, int $userIdOrDivId, string $column): void
    {

        $actuals = $this->actuals(
            $userIdOrDivId,
            $column,
            $doctors->pluck('doctor_id')->toArray()
        );
        $doctors->each(function ($doctor) use (
            $actuals
        ) {
            $frequency = $doctor->frequency;
            $actual = $actuals->where('account_dr_id', $doctor->id)->count();
            if ($frequency === 0) {
                $this->analyzerFrequencyService
                    ->addTo(
                        AnalyzerFrequency::BELOW_FREQUENCY,
                        1
                    );
                return;
            }
            if ($frequency == $actual)
                $this->analyzerFrequencyService
                    ->addTo(
                        AnalyzerFrequency::MEET_FREQUENCY,
                        1
                    );
            if ($frequency > $actual)
                $this->analyzerFrequencyService
                    ->addTo(
                        AnalyzerFrequency::BELOW_FREQUENCY,
                        1
                    );
            if ($frequency < $actual)
                $this->analyzerFrequencyService
                    ->addTo(
                        AnalyzerFrequency::ABOVE_FREQUENCY,
                        1
                    );
        });
    }

    private function frequencyType($doctor, array $months, $year): Collection
    {
        $freqType = OtherSetting::where("key", "type_of_frequency")->value("value");
        return match ($freqType) {
            1 => ClassFrequency::select([
                'id',
                'line_id',
                'class_id',
                'class_frequencies.frequency as frequency',
                'class_frequencies.date as date'
            ])
                ->where('class_frequencies.class_id', $doctor['doc_class_id'])
                ->whereBetween(DB::raw("(DATE_FORMAT(crm_class_frequencies.date,'%m'))"), $months)
                ->whereYear('class_frequencies.date', $year)
                ->groupBy('id', 'class_frequencies.frequency')->get(),
            2 => DoctorFrequency::select([
                'id',
                'line_id',
                'doctor_id',
                'account_id',
                'date',
                'doctor_frequencies.frequency as frequency',
                'doctor_frequencies.date as date'
            ])
                ->where('doctor_frequencies.doctor_id', $doctor['id'])
                ->where('doctor_frequencies.account_id', $doctor['account_id'])
                ->whereBetween(DB::raw("(DATE_FORMAT(crm_doctor_frequencies.date,'%m'))"), $months)
                ->whereYear('doctor_frequencies.date', $year)
                ->groupBy('id', 'doctor_frequencies.frequency')->get(),
            3 => SpecialityFrequency::select([
                'id',
                'line_id',
                'speciality_id',
                'speciality_frequencies.frequency as frequency',
                'speciality_frequencies.date as date'
            ])
                ->where('speciality_frequencies.speciality_id', $doctor['speciality_id'])
                ->whereBetween(DB::raw("(DATE_FORMAT(crm_speciality_frequencies.date,'%m'))"), $months)
                ->whereYear('speciality_frequencies.date', $year)
                ->groupBy('id', 'speciality_frequencies.frequency')->get(),
            default => collect(),
        };
    }

    private function actuals(int $userIdOrDivId, string $column, array $doctors): Collection
    {

        $data = ActualVisit::select([
            'actual_visits.id as id',
            'actual_visits.visit_date',
            'actual_visits.account_dr_id',
            DB::raw("DATE_FORMAT(crm_actual_visits.created_at,'%Y-%m-%d %H:%s:%i') as date"),
            'specialities.id as speciality_id',
            'bricks.name as brick',
            'lines.name as line',
            'line_divisions.name as division',
            'users.fullname as user',
            'accounts.name as account',
            'accounts.id as account_id',
            'account_types.name as acc_type',
            DB::raw('IFNULL(crm_doctors.name,"") as doctor'),
            DB::raw('IFNULL(crm_doctors.id,"") as doctor_id'),
            DB::raw('IFNULL(group_concat(distinct crm_products.name),"") as product'),
            'specialities.name as speciality',
            'visit_types.name as type',
            'plan_visit_details.approval as status'
        ])
            ->leftJoin('lines', 'actual_visits.line_id', 'lines.id')
            ->leftJoin('line_divisions', 'actual_visits.div_id', 'line_divisions.id')
            ->leftJoin('users', 'actual_visits.user_id', 'users.id')
            ->leftJoin('accounts', 'actual_visits.account_id', 'accounts.id')
            ->leftJoin('bricks', 'actual_visits.brick_id', 'bricks.id')
            ->leftJoin('account_types', 'actual_visits.acc_type_id', 'account_types.id')
            ->leftJoin('doctors', 'actual_visits.account_dr_id', 'doctors.id')
            ->leftJoin('specialities', 'doctors.speciality_id', 'specialities.id')
            ->leftJoin('visit_types', 'actual_visits.visit_type_id', 'visit_types.id')
            ->leftJoin(
                'plan_visit_details',
                function ($join) {
                    $join->on('actual_visits.id', '=', 'plan_visit_details.visitable_id');
                    $join->where('plan_visit_details.visitable_type', 'App\ActualVisit');
                }
            )
            ->leftJoin('actual_visit_products', 'actual_visits.id', 'actual_visit_products.visit_id')
            ->leftJoin('products', 'actual_visit_products.product_id', 'products.id')
            ->where('actual_visits.deleted_at', '=', null)
            ->whereIntegerInRaw('actual_visits.account_dr_id', $doctors)
            ->where(fn($q) => $q->where('plan_visit_details.approval', 1)->orWhere('plan_visit_details.approval', null))
            ->where($column, $userIdOrDivId)
            ->whereBetween(DB::raw("(DATE_FORMAT(crm_actual_visits.visit_date,'%m'))"), $this->months)
            ->whereYear('visit_date', $this->year);

        return $data->groupBy('id', 'plan_visit_details.approval')->get();
    }
}
