<?php

namespace App\Http\Controllers\Analyzer;

use App\Distributor;
use App\DivisionType;
use App\Exceptions\CrmException;
use App\Http\Controllers\Controller;
use App\Line;
use App\LineDivision;
use App\Models\Distributors\DistributorLine;
use App\Product;
use App\Productprice;
use App\Sale;
use App\SaleDetail;
use App\Services\Analyzers\AnalyzerSaleService;
use App\Services\Enums\Ceiling;
use App\TargetDetails;
use App\User;
use Carbon\CarbonPeriod;
use Illuminate\Database\Eloquent\Collection;
use \Illuminate\Support\Collection as SupportCollection;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class SalesAnalyzerController extends Controller
{
    private $cacheTimeout = 60;
    private AnalyzerSaleService $analyzerSaleService;
    // employee and division
    private bool $isFilterByEmployee = false;

    // doctor and account
    private bool $isFilterByValues = false;
    private Carbon $from;
    private Carbon $to;

    private array $months;

    private string $year;

    private CarbonPeriod $period;
    private int $divisionType;
    // Sale filter
    private array $divisionsPerLine;
    private array $salesFilter;
    private Collection $filtered;
    /**
     * @var \App\Product[]|Collection|\LaravelIdea\Helper\App\_IH_Product_C
     */
    private Collection $products;
    private SupportCollection $distributors;

    public function __construct()
    {
        // Don't set user in constructor for Octane compatibility
        $this->filtered = new Collection();
        $this->divisionsPerLine = [];
        $this->analyzerSaleService = new AnalyzerSaleService();
    }

    private function setSaleFilters()
    {
        $this->isFilterByEmployee = $this->salesFilter['filter'] == 2;
        $this->isFilterByValues = $this->salesFilter['sales_by'] == 2;
    }

    private function setDateBoundaries()
    {
        $this->from = Carbon::parse($this->salesFilter['fromDate'])->startOfDay();
        $this->to = Carbon::parse($this->salesFilter['toDate'])->endOfDay();

        $this->months = [
            (int)$this->from->format('m'),
            (int)$this->to->format('m')
        ];

        $this->year = (int)Carbon::parse($this->from)->format('Y');

        $this->period = CarbonPeriod::create($this->from, '1 month', $this->to);
    }


    public function filter(Request $request)
    {
        $this->salesFilter = $request->saleFilter;
        $this->setSaleFilters();
        $this->setDateBoundaries();

        $this->divisionType = DivisionType::where('last_level', '=', 1)
            ->value('id');


        $period = $period = CarbonPeriod::create(Carbon::now()->firstOfYear(), '1 month', Carbon::now()->endOfYear());
        $ytd[] = [
            'Line',
            'Sales',
            'Month'
        ];
        $ytd = collect($ytd);
        $totalLines = Line::select('id', 'name')->where('from_date', '<=', now())
            ->where(
                fn($q) => $q->where('to_date', null)
                    ->orWhere('to_date', '>=', now())
            )->get();
        foreach ($totalLines as $line) {
            foreach ($period as $month) {
                $from = $month;
                $to = $month->endOfMonth();
                $distributors = DistributorLine::where('line_id', $line->id)
                    ->where('from_date', '<=', $from)
                    ->where(
                        fn($q) => $q->where('to_date', null)
                            ->orWhere('to_date', '>=', $to)
                    )
                    ->with('distributor:id,name')->get()
                    ->pluck('distributor')->unique('id')->values();

                $products = $line->products($from, $to)->get();
                $divisions = $this->isFilterByEmployee
                    ? $this->ytdFilterByEmployee($line, $from, $to)
                    : $this->ytdFilterByDivision($line, $from, $to);
                $achieve = $this->getYtd($divisions, $products, $distributors, $month, $this->year);
                $ytd = $ytd->push([
                    $line->name,
                    $achieve,
                    $month->format('Y-m')
                ]);
            }
        }
        $lines = Line::when(
            !empty($this->salesFilter['lines']),
            fn($q) => $q->whereIntegerInRaw(
                "lines.id",
                $this->salesFilter['lines']
            )
        )->get();
        $lines->each(function (Line $line) {
            $this->distributors = DistributorLine::where('line_id', $line->id)
                ->when(
                    !empty($this->salesFilter['distributors']),
                    fn($q) => $q->whereIntegerInRaw('distributor_id', $this->salesFilter['distributors'])
                )
                ->where('from_date', '<=', now())
                ->where(
                    fn($q) => $q->where('to_date', null)
                        ->orWhere('to_date', '>=', now())
                )
                ->with('distributor:id,name')->get()
                ->pluck('distributor')->unique('id')->values();

            $this->analyzerSaleService->addDistributors($this->distributors);
            $this->products = $line->products()->when(
                !empty($this->salesFilter['products']),
                fn($q) => $q->whereIntegerInRaw(
                    "line_products.product_id",
                    $this->salesFilter['products']
                )
            )->get();
            $this->analyzerSaleService->addProducts($this->products);

            // throw new CrmException($period);
            $this->isFilterByEmployee
                ? $this->whenFilterByEmployee($line)
                : $this->whenFilterByDivision($line);
        });
        $salesTarget = $this->getSalesTarget(
            lines: $lines->pluck('id')->toArray(),
            divisions: $this->filtered->unique('id')->pluck('id')->toArray(),
            products: $this->salesFilter['products'],
            distributors: $this->salesFilter['distributors'],
            period: $this->period,
            year: $this->year,
        );
        $max = null;
        $achieve = $salesTarget[1]['value'] > 0 ? round(($salesTarget[0]['value'] / $salesTarget[1]['value']) * 100, 2) : 0;
        // $achieve = 60;
        $max = match (true) {
            $achieve < 50.00 => 50,
            $achieve >= 50.00, $achieve < 100.00 => 100,
            $achieve >= 100.00, $achieve < 150.00 => 150,
            $achieve >= 150.00, $achieve < 200.00 => 200,
        };
        $achievements = [
            [
                'name' => 'sales',
                'data' => [
                    [
                        'name' => 'max',
                        'value' => $max,
                    ],
                    [
                        'name' => 'achievement',
                        'value' => $achieve,
                    ]
                ],
            ]
        ];
        return response()->json([
            'ytd' => $ytd,
            'salesTargets' => $salesTarget,
            'achievements' => $achievements,
            'distributors' => $this->analyzerSaleService->getPerDistributor(),
            'products' => $this->analyzerSaleService->getPerProduct(),
            'structure' => LineDivision::salesPerDivision(
                divisionIds: $this->salesFilter['divisions'],
                lineIds: $this->salesFilter['lines'],
                productIds: $this->salesFilter['products'],
                distIds: $this->salesFilter['distributors'],
                from: $this->from,
                to: $this->to,
                label: $this->isFilterByValues ? "value" : "quantity"
            ),
            'users' => SaleDetail::bestAchieversPerValues(
                period: $this->period,
                year: $this->year,
                division_type: $this->divisionType,
                divisionIds: LineDivision::whereIn('id', $this->salesFilter['divisions'])->where('division_type_id', $this->divisionType)->where('is_kol', 0)->get()->pluck('id')->toArray(),
                users: User::whereHas('divisions', fn($q) => $q->where('division_type_id', $this->divisionType)->where('is_kol', 0))
                    ->whereIn('id', $this->salesFilter['users'])->get(),
                lineIds: $this->salesFilter['lines'],
                productIds: $this->salesFilter['products'],
                distIds: empty($this->salesFilter['distributors']) ?
                    Distributor::pluck('id')->toArray() :
                    $this->salesFilter['distributors'],
                from: $this->from,
                to: $this->to,
                howMany: $howMany = 10,
            )
        ]);
    }

    private function whenFilterByEmployee(Line $line): void
    {
        $users = $line->users()->wherePivot("deleted_at", null)
            ->when(
                !empty($this->salesFilter['users']),
                fn($q) => $q->whereIn("line_users.user_id", $this->salesFilter['users'])
            )->get();
        $belowDivisions = collect();
        Auth::user()->filterUsers($line, $users, $this->salesFilter)
            ->each(fn(User $user) => (
                $belowDivisions->push(
                    $user->allBelowDivisions($line)
                        ->where('division_type_id', '=', $this->divisionType)
                        ->where('is_kol', '=', 0)
                )
            ));
        $belowDivisions = $belowDivisions->flatten();
        $this->divisionsPerLine = $belowDivisions->unique('id')->pluck('id')->toArray();
        $this->filtered = $this->filtered->merge($belowDivisions);
        $this->products->each(fn(Product $product) => $this->loadSales($this->divisionsPerLine, $product, $line));
    }
    private function ytdFilterByEmployee(Line $line)
    {
        $users = $line->users()->wherePivot("deleted_at", null)
            ->when(
                !empty($this->salesFilter['users']),
                fn($q) => $q->whereIn("line_users.user_id", $this->salesFilter['users'])
            )->get();
        $belowDivisions = collect();
        Auth::user()->filterUsers($line, $users, $this->salesFilter)
            ->each(fn(User $user) => (
                $belowDivisions->push(
                    $user->allBelowDivisions($line)
                        ->where('division_type_id', '=', $this->divisionType)
                        ->where('is_kol', '=', 0)
                )
            ));
        $belowDivisions = $belowDivisions->flatten();
        return $belowDivisions->unique('id')->pluck('id')->toArray();
    }
    private function ytdFilterByDivision(Line $line)
    {
        $divisions = $line->divisions()->where("deleted_at", null)
            ->when(
                !empty($this->salesFilter['divisions']),
                fn($q) => $q->whereIn("line_divisions.id", $this->salesFilter['divisions'])
            )->get();
        $belowDivisions = collect();
        Auth::user()->filterDivisions($line, $divisions, $this->salesFilter)?->each(fn(LineDivision $division) => ($belowDivisions->push($division->getBelowDivisions()
            ->where('division_type_id', '=', $this->divisionType)->where('is_kol', '=', 0))
        ));
        $belowDivisions = $belowDivisions->flatten();
        return $belowDivisions->unique('id')->pluck('id')->toArray();
    }
    private function whenFilterByDivision(Line $line): void
    {
        $divisions = $line->divisions()->where("deleted_at", null)
            ->when(
                !empty($this->salesFilter['divisions']),
                fn($q) => $q->whereIn("line_divisions.id", $this->salesFilter['divisions'])
            )->get();
        $belowDivisions = collect();
        Auth::user()->filterDivisions($line, $divisions, $this->salesFilter)
            ->each(fn(LineDivision $division) => ($belowDivisions->push($division->getBelowDivisions()
                ->where('division_type_id', '=', $this->divisionType)->where('is_kol', '=', 0))
            ));
        $belowDivisions = $belowDivisions->flatten();
        $this->divisionsPerLine = $belowDivisions->unique('id')->pluck('id')->toArray();
        $this->filtered = $this->filtered->merge($belowDivisions);
        $this->products->each(fn(Product $product) => $this->loadSales($this->divisionsPerLine, $product, $line));
    }
    private function getYTD($divisions, $products, $distributors, $month, $year)
    {
        $sales = 0.0;
        $target = 0.0;
        foreach ($products as $product) {
            foreach ($distributors as $distributor) {
                $sales += $this->ytdSales($divisions, $product, $distributor, $month, $year);
                $target += $this->ytdTargets($divisions, $product, $month, $year);
            }
        }
        // $achieve = $target > 0 ? round($sales / $target * 100, 2) : 0;
        $achieve = $sales;
        return $achieve;
    }
    private function loadSales(array $divisions, Product $product, Line $line)
    {
        $this->distributors->each(function (Distributor $distributor) use ($line, $divisions, $product) {
            foreach ($this->period as $month) {
                $sales = Sale::select(
                    [
                        'sales.id as sale_id',
                        'sales_details.id as detail_id',
                        'sales_details.div_id as div_id',
                        'sales_details.brick_id as brick_id',
                        'sales.product_id',
                        'sales_details.date',
                        'mappings.distributor_id',
                        'products.name as product',
                        'sales_details.value as value',
                        'sales_details.quantity as quantity',
                        'product_prices.avg_price as avg_price'
                    ]
                )
                    ->selectRaw('crm_product_prices.avg_price * crm_sales_details.quantity  as sales_value')
                    ->leftJoin('mapping_sale', 'sales.id', 'mapping_sale.sale_id')
                    ->leftJoin('mappings', 'mapping_sale.mapping_id', 'mappings.id')
                    ->leftJoin('sales_details', 'sales.id', 'sales_details.sale_id')
                    ->leftJoin('products', 'sales.product_id', 'products.id')
                    ->leftJoin(
                        'product_prices',
                        fn($join) => $join->on('products.id', 'product_prices.product_id')
                            ->where('product_prices.from_date', '<=', $month)
                            ->where(fn($q) => $q->where('product_prices.to_date', '>', (string)Carbon::parse($month)->endOfMonth()->toDateString())
                                ->orWhere('product_prices.to_date', null))
                    )
                    ->where('sales.product_id', $product->id)
                    ->where(fn($q) => $q->where('mappings.line_id', $line->id)->orWhere('mappings.line_id', null))
                    ->where(fn($q) => $q->where('mappings.distributor_id', $distributor?->id)->orWhere('mappings.distributor_id', null))
                    ->whereIntegerInRaw('sales_details.div_id', $divisions)
                    ->where(DB::raw("(DATE_FORMAT(crm_sales_details.date,'%m'))"), $month->format('m'))
                    ->whereYear('sales_details.date', $this->year)
                    ->get()->unique('detail_id')->values();

                $sum = $this->isFilterByValues
                    ? ($sales->sum('values') ?: $sales->sum('avg_price'))
                    : $sales->sum('quantity');


                $this->analyzerSaleService->incrementProduct($product->name, $sum);
                $this->analyzerSaleService->incrementDistributor($distributor->name, $sum);
            }
        });
    }
    private function getSalesTarget(array $lines, array $divisions, array $products, array $distributors, $period, $year)
    {
        $totalSalesUnits = 0;
        $totalSalesValues = 0;
        $totalTargetUnits = 0;
        $totalTargetValues = 0;
        foreach ($products as $product) {
            $sales = $this->sales($lines, $divisions, $product, $distributors, $period, $year);
            $targets = $this->targets($divisions, $product, $period, $year);
            $totalSalesUnits += $sales['salesUnit'];
            $totalSalesValues += $sales['salesValue'];
            $totalTargetUnits += $targets['targetUnit'];
            $totalTargetValues += $targets['targetValue'];
        }

        if ($this->isFilterByValues) {
            $sales = collect([
                'name' => 'Sales Value : ' . number_format($totalSalesValues, 2),
                'value' => round($totalSalesValues, 2),
            ]);
            $target = collect([
                'name' => 'Target Value : ' . number_format($totalTargetValues),
                'value' => round($totalTargetValues, 2),
            ]);
            $results = collect([$sales, $target]);
        } else {
            $sales = collect([
                'name' => 'Sales Units : ' . $totalSalesUnits,
                'value' => round($totalSalesUnits, 0),
            ]);
            $target = collect([
                'name' => 'Target Units : ' . $totalTargetUnits,
                'value' => round($totalTargetUnits, 0),
            ]);
            $results = collect([$sales, $target]);
        }
        return $results;
    }

    private function sales($lines, $divisions, $product, $distributors, $period, $year)
    {
        $cacheKey = "sales_data_" . md5(serialize(func_get_args()));
        return $this->getCachedSalesData($cacheKey, function () use ($period, $divisions, $product, $distributors, $year) {
            $salesUnit = 0.0;
            $salesValue = 0.0;
            foreach ($distributors as $distributor) {
                $sales = collect();
                foreach ($period as $month) {
                    $data = Sale::select([
                        'sales.id as sale_id',
                        'sales_details.id as detail_id',
                        'sales_details.div_id as div_id',
                        'sales.product_id as product_id',
                        'sales.distributor_id as distributor_id',
                        'lines.id as line_id',
                        'products.name as product',
                        'sales_details.quantity as quantity',
                        'sales_details.value as value',
                        'product_prices.avg_price as avg_price'
                    ])
                        ->selectRaw('crm_product_prices.avg_price * crm_sales_details.quantity  as sales_value')
                        ->whereIn("ceiling", [Ceiling::DISTRIBUTED, Ceiling::BELOW])
                        ->where('sales.distributor_id', $distributor)
                        ->leftJoin('sales_details', 'sales.id', 'sales_details.sale_id')
                        ->leftJoin('line_divisions', 'sales_details.div_id', 'line_divisions.id')
                        ->leftJoin('lines', 'line_divisions.line_id', 'lines.id')
                        ->leftJoin('products', 'sales.product_id', 'products.id')
                        ->leftJoin(
                            'product_prices',
                            fn($join) => $join->on('products.id', 'product_prices.product_id')
                                ->where(fn($q) => $q->where('product_prices.distributor_id', $distributor)->orWhereNull('product_prices.distributor_id'))
                                ->where('product_prices.from_date', '<=', $month)
                                ->where(fn($q) => $q->where('product_prices.to_date', '>=', (string)Carbon::parse($month)->endOfMonth()->toDateString())
                                    ->orWhere('product_prices.to_date', null))
                        )
                        ->where('sales.product_id', $product)
                        ->whereIntegerInRaw('sales_details.div_id', $divisions)
                        ->where(DB::raw("(DATE_FORMAT(crm_sales_details.date,'%m'))"), $month->format('m'))->whereYear('sales_details.date', $year);
                    $data = $data->get()->unique('detail_id')->values();
                    $sales = $sales->push($data);
                }
                $sales = $sales->collapse();

                $quantity = $sales->sum('quantity');
                $value = $sales->sum('value');
                if ($value == 0.0) {
                    $value = $sales->sum('sales_value');
                }
                $salesUnit += $quantity;
                $salesValue += $value;
            }
            return ['salesUnit' => round($salesUnit, 0), 'salesValue' => $salesValue];
        });
    }

    private function targets($divisions, $product, $period, $year)
    {
        $cacheKey = "targets_data_" . md5(serialize(func_get_args()));
        return $this->getCachedTargetsData($cacheKey, function () use ($divisions, $product, $period, $year) {
            $targetUnit = 0;
            $targetValue = 0;
            $targets = collect([]);
            foreach ($period as $month) {
                $data = TargetDetails::select([
                    'target_details.id',
                    'products.id as product_id',
                    'product_prices.id as product_price_id',
                    'target_details.div_id',
                    'target_details.brick_id',
                    'target_details.target',
                    'product_prices.avg_price as avg_price'
                ])
                    ->selectRaw('crm_product_prices.avg_price * crm_target_details.target  as target_value')
                    ->leftJoin('products', 'target_details.product_id', 'products.id')
                    ->leftJoin(
                        'product_prices',
                        fn($join) => $join->on('products.id', 'product_prices.product_id')
                            ->where('product_prices.from_date', '<=', $month)
                            ->where(fn($q) => $q->where('product_prices.to_date', '>', (string)Carbon::parse($month)->endOfMonth()->toDateString())
                                ->orWhere('product_prices.to_date', null))
                    )
                    ->where('products.id', $product)
                    ->whereIntegerInRaw('div_id', $divisions)
                    ->where(DB::raw("(DATE_FORMAT(date,'%m'))"), $month->format('m'))->whereYear('date', $year)
                    ->orderBy('product_prices.id', 'ASC');
                $data = $data->get()->unique('id')->values();
                $targets = $targets->push($data);
            }
            $targets = $targets->collapse();
            $targetUnit += $targets->sum('target');
            $targetValue += $targets->sum('target_value');
            return array('targetUnit' => round($targetUnit, 0), 'targetValue' => $targetValue);
        });
    }

    private function ytdSales($divisions, $product, $distributor, $month, $year)
    {
        // Log::info($month);
        // throw new CrmException()
        $cacheKey = "ytd_sales_dataa_" . md5(serialize(func_get_args()));
        return $this->getCachedSalesData($cacheKey, function () use ($divisions, $product, $distributor, $month, $year) {
            $data = Sale::select([
                'sales.id as sale_id',
                'sales_details.id as detail_id',
                'sales_details.div_id as div_id',
                'sales.product_id as product_id',
                'sales.distributor_id as distributor_id',
                'products.name as product',
                'sales_details.quantity as quantity',
                'sales_details.value as value',
                'product_prices.avg_price as avg_price'
            ])
                ->selectRaw('crm_product_prices.avg_price * crm_sales_details.quantity  as sales_value')
                ->whereIn("ceiling", [Ceiling::DISTRIBUTED, Ceiling::BELOW])
                ->where('sales.distributor_id', $distributor->id)
                ->leftJoin('sales_details', 'sales.id', 'sales_details.sale_id')
                ->leftJoin('line_divisions', 'sales_details.div_id', 'line_divisions.id')
                ->leftJoin('lines', 'line_divisions.line_id', 'lines.id')
                ->leftJoin('products', 'sales.product_id', 'products.id')
                ->leftJoin(
                    'product_prices',
                    fn($join) => $join->on('products.id', 'product_prices.product_id')
                        ->where(fn($q) => $q->where('product_prices.distributor_id', $distributor->id)
                            ->orWhereNull('product_prices.distributor_id'))
                        ->where('product_prices.from_date', '<=', $month)
                        ->where(fn($q) => $q->where('product_prices.to_date', '>=', (string)Carbon::parse($month)->endOfMonth()->toDateString())
                            ->orWhere('product_prices.to_date', null))
                )
                ->where('sales.product_id', $product->id)
                ->whereIntegerInRaw('sales_details.div_id', $divisions)
                ->whereBetween('sales_details.date', [Carbon::parse($month)->firstOfMonth()->toDateString(), Carbon::parse($month)->endOfMonth()->toDateString()])
                // ->where(DB::raw("(DATE_FORMAT(crm_sales_details.date,'%m'))"), $month->format('m'))
                // ->whereYear('sales_details.date', $year);
                ->get()->unique('detail_id')->values();
            $value = $data->sum('value');
            if ($value == 0.0) {
                $value = $data->sum('sales_value');
            }
            return $value;
        });
    }

    private function ytdTargets($divisions, $product, $month, $year)
    {
        $cacheKey = "targets_data_" . md5(serialize(func_get_args()));
        return $this->getCachedTargetsData($cacheKey, function () use ($divisions, $product, $month, $year) {
            $data = TargetDetails::select([
                'target_details.id',
                'products.id as product_id',
                'product_prices.id as product_price_id',
                'target_details.div_id',
                'target_details.brick_id',
                'target_details.target',
                'product_prices.avg_price as avg_price'
            ])
                ->selectRaw('crm_product_prices.avg_price * crm_target_details.target  as target_value')
                ->leftJoin('products', 'target_details.product_id', 'products.id')
                ->leftJoin(
                    'product_prices',
                    fn($join) => $join->on('products.id', 'product_prices.product_id')
                        ->where('product_prices.from_date', '<=', $month)
                        ->where(fn($q) => $q->where('product_prices.to_date', '>', (string)Carbon::parse($month)->endOfMonth()->toDateString())
                            ->orWhere('product_prices.to_date', null))
                )
                ->where('products.id', $product->id)
                ->whereIntegerInRaw('div_id', $divisions)
                ->whereBetween('date', [Carbon::parse($month)->firstOfMonth()->toDateString(), Carbon::parse($month)->endOfMonth()->toDateString()])
                ->orderBy('product_prices.id', 'ASC');
            $data = $data->get()->unique('id')->values();

            $targetValue = $data->sum('target_value');
            return $targetValue;
        });
    }
    private function getCachedSalesData($key, $callback)
    {
        return Cache::remember($key, $this->cacheTimeout, $callback);
    }

    private function getCachedTargetsData($key, $callback)
    {
        return Cache::remember($key, $this->cacheTimeout, $callback);
    }
}
