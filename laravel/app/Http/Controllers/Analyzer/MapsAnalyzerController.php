<?php

namespace App\Http\Controllers\Analyzer;

use App\ActualVisit;
use App\CallRate;
use App\ClassFrequency;
use App\DivisionType;
use App\DoctorFrequency;
use App\Http\Controllers\Controller;
use App\Line;
use App\LineDivision;
use App\Models\OffDay;
use App\Models\OtherSetting;
use App\OwActualVisit;
use App\Services\AccountService;
use App\Services\ActualService;
use App\Services\Analyzers\AnalyzerCallRateService;
use App\Services\Analyzers\AnalyzerCoverageService;
use App\Services\Analyzers\AnalyzerFrequencyService;
use App\Services\DoctorService;
use App\Services\Enums\AnalyzerCallRate;
use App\Services\Enums\AnalyzerCoverage;
use App\Services\Enums\AnalyzerFrequency;
use App\SpecialityFrequency;
use App\User;
use App\Vacation;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class MapsAnalyzerController extends Controller
{
    private AnalyzerCoverageService $coverageAnalyzer;
    private AnalyzerCallRateService $callRateAnalyzer;
    private AnalyzerFrequencyService $analyzerFrequencyService;

    // employee and division
    private bool $isFilterByEmployee = false;

    // doctor and account
    private bool $isFilterByDoctorType = false;
    private Carbon $from;
    private Carbon $to;
    private int $divisionType;
    // visit filter
    private array $visitFilter;
    private User $user;

    public function __construct()
    {
        $this->coverageAnalyzer = new AnalyzerCoverageService();
        $this->callRateAnalyzer = new AnalyzerCallRateService();
        $this->analyzerFrequencyService = new AnalyzerFrequencyService();
    }

    private function setVisitFilters()
    {
        $this->isFilterByEmployee = $this->visitFilter['filter'] == 2;
        $this->isFilterByDoctorType = $this->visitFilter['type'] == 2;
    }

    private function setDateBoundaries()
    {
        $this->from = Carbon::parse($this->visitFilter['fromDate'])->startOfDay();
        $this->to = Carbon::parse($this->visitFilter['toDate'])->endOfDay();
    }

    private function getDivisionsFilteredByDivisions($lines)
    {
        $filtered = collect();
        foreach ($lines as $line) {
            $divisions = $line->divisions()->where("deleted_at", null)
                ->when(!empty($this->visitFilter['divisions']), fn($q) => $q->whereIntegerInRaw("line_divisions.id", $this->visitFilter['divisions']))->get();
            $filtered = $filtered->merge(Auth::user()->filterDivisions($line, $divisions, $this->visitFilter));

        }
        return $filtered;
    }

    private function getUsersFilteredByEmployees($lines)
    {
        $filtered = collect();
        foreach ($lines as $line) {
            $users = $line->users($this->from, $this->to)
                ->when(!empty($this->visitFilter['users']), fn($q) => $q->whereIntegerInRaw("line_users.user_id", $this->visitFilter['users']))->get();
            $filtered = $filtered->merge(Auth::user()->filterUsers($line, $users, $this->visitFilter));
        }
        return $filtered;
    }

    public function filter(Request $request)
    {
        $this->visitFilter = $request->visitFilter;
        $this->setVisitFilters();
        $this->setDateBoundaries();

        $lines = Line::when(
            !empty($visit['lines']),
            fn($q) => $q->whereIntegerInRaw("lines.id", $this->visitFilter['lines'])
        )->get();

        $this->divisionType = DivisionType::where('last_level', '=', 1)->value('id');

        $this->isFilterByEmployee
            ? $this->whenFilterByEmployee($lines)
            : $this->whenFilterByDivision($lines);

        return response()->json([
            'coverage' => $this->isFilterByDoctorType
                ? $this->coverageAnalyzer->getDoctorCoverage()
                : $this->coverageAnalyzer->getAccountCoverage(),
            'call_rate' => $this->callRateAnalyzer->all(),
            'frequency' => $this->analyzerFrequencyService->all(),
        ]);
    }

    private function whenFilterByEmployee($lines): void
    {
        $this->getUsersFilteredByEmployees($lines)->each(function ($user) {
            [$lines, $divisionHolders] = $this->getLinesBelowDivisionsBelowUsers($user);
            $divIds = $divisionHolders->pluck('div_id')->toArray();
            $usersIds = $divisionHolders->pluck('user_id')->toArray();

            $doctorsAndAccounts = $this->countAccountsAndDoctors($lines, $divIds);
            $this->coveredAccountsAndDoctors($lines, $doctorsAndAccounts, $usersIds, 'user_id');
            $divisionHolders->each(
                function ($divisionHolder) use ($doctorsAndAccounts, $lines) {
                    $this->calculateCallRate($divisionHolder, $lines);
                    $this->calculateFrequency(
                        $doctorsAndAccounts['doctors'],
                        $divisionHolder->user_id,
                        'users.id'
                    );
                }
            );
        });
    }

    private function whenFilterByDivision($lines): void
    {
        $this->getDivisionsFilteredByDivisions($lines)->each(function ($division) {
            [$lines, $divisionHolders] = $this->getLinesBelowDivisions($division);
            $divIds = $divisionHolders->pluck('div_id')->toArray();
            $doctorsAndAccounts = $this->countAccountsAndDoctors($lines, $divIds);
            $this->coveredAccountsAndDoctors($lines, $doctorsAndAccounts, $divIds, 'div_id');
            $divisionHolders->each(
                function ($divisionHolder) use ($doctorsAndAccounts, $lines) {
                    $this->calculateCallRate($divisionHolder, $lines);
                    $this->calculateFrequency(
                        $doctorsAndAccounts['doctors'],
                        $divisionHolder->div_id,
                        'line_divisions.id'
                    );
                }
            );
        });
    }

    private function getLinesBelowDivisions(LineDivision $division): array
    {
        $lines = $division->line()->get()->pluck('id')->toArray();
        $divisions = $division->user($this->from, $this->to)->allBelowUsersWithDivision()
            ->where('div_type_id', $this->divisionType)
            ->unique('user_id');

        return [$lines, $divisions];
    }

    private function getLinesBelowDivisionsBelowUsers(User $user): array
    {
        $lines = $user->lines->pluck('id')->toArray();
        $divisions = $user->allBelowUsersWithDivision()
            ->where('div_type_id', $this->divisionType)
            ->unique('user_id');
        return [$lines, $divisions];
    }


    /**
     * @throws \Exception
     */
    private function countAccountsAndDoctors(array $lines, array $divisions): array
    {

        $doctors = $this->isFilterByDoctorType
            ? (new DoctorService)->getDoctorsWithFrequency(
                lines: $lines,
                divisions: $divisions,
                from: $this->from,
                to: $this->to,
                months: [$this->from->format('m'), $this->to->format('m')],
                year: $this->from->format('Y'),
                specialities: $this->visitFilter['specialities'],
                accountTypes: $this->visitFilter['accountTypes'],
            )
            : collect();

        $accounts = !$this->isFilterByDoctorType
            ? (new AccountService)->getAccounts(
                lines: $lines,
                divisions: $divisions,
                specialities: $this->visitFilter['specialities'],
                accountTypes: $this->visitFilter['accountTypes']
            )
            : collect();
        $countPhDoctors = $doctors->where("acc_shift_id", 3)->count();
        $countAmDoctors = $doctors->where("acc_shift_id", 1)->count();
        $countPmDoctors = $doctors->where("acc_shift_id", 2)->count();
        $countPhAccounts = $accounts->where("acc_shift_id", 3)->count();
        $countAmAccounts = $accounts->where("acc_shift_id", 1)->count();
        $countPmAccounts = $accounts->where("acc_shift_id", 2)->count();


        $this->coverageAnalyzer->addTo(
            AnalyzerCoverage::PH_ACCOUNT_COVERAGE,
            0,
            $countPhAccounts
        );
        $this->coverageAnalyzer->addTo(
            AnalyzerCoverage::AM_ACCOUNT_COVERAGE,
            0,
            $countAmAccounts
        );
        $this->coverageAnalyzer->addTo(
            AnalyzerCoverage::PM_ACCOUNT_COVERAGE,
            0,
            $countPmAccounts
        );
        $this->coverageAnalyzer->addTo(
            AnalyzerCoverage::PH_DOCTOR_COVERAGE,
            0,
            $countPhDoctors
        );
        $this->coverageAnalyzer->addTo(
            AnalyzerCoverage::AM_DOCTOR_COVERAGE,
            0,
            $countAmDoctors
        );
        $this->coverageAnalyzer->addTo(
            AnalyzerCoverage::PM_DOCTOR_COVERAGE,
            0,
            $countPmDoctors
        );

        return [
            'accounts' => $accounts,
            'doctors' => $doctors,
        ];
    }


    private function getVisitsFilteredByDoctors(
        array      $belowDivisions,
        array      $lines,
        Collection $doctors,
        string     $filterBy
    ): Collection
    {
        $visits = collect();
        foreach ($belowDivisions as $division) {

            $visits = $visits->merge(
                ActualVisit::whereIntegerInRaw('line_id', $lines)
                    ->where($filterBy, $division)
                    ->whereIntegerInRaw('account_dr_id', $doctors->pluck('doctor_id'))
                    ->whereBetween('visit_date', [$this->from, $this->to])
                    ->get()->unique('account_dr_id')->values()
            );
        }

        return $visits;
    }

    private function getVisitsFilteredByAccounts(
        array      $belowDivisions,
        array      $lines,
        Collection $accounts,
        string     $filterBy
    ): Collection
    {
        $visits = collect();
        foreach ($belowDivisions as $division) {

            $visits = $visits->merge(
                ActualVisit::whereIntegerInRaw('line_id', $lines)
                    ->where($filterBy, $division)
                    ->whereIntegerInRaw('account_id', $accounts->pluck('account_id'))
                    ->whereBetween('visit_date', [$this->from, $this->to])
                    ->get()->unique('account_id')->values()
            );
        }

        return $visits;
    }

    /**
     * @throws \Exception
     */
    public function coveredAccountsAndDoctors(array $lines, array $count, array $Ids, string $filterBy): void
    {
        if ($this->isFilterByDoctorType) {
            $phDoctors = $count['doctors']->where("acc_shift_id", 3);
            $amDoctors = $count['doctors']->where("acc_shift_id", 1);
            $pmDoctors = $count['doctors']->where("acc_shift_id", 2);
            $phCoverage = $this->getVisitsFilteredByDoctors($Ids, $lines, $phDoctors, $filterBy)
                ->pluck('account_dr_id')->count();
            $amCoverage = $this->getVisitsFilteredByDoctors($Ids, $lines, $amDoctors, $filterBy)
                ->pluck('account_dr_id')->count();
            $pmCoverage = $this->getVisitsFilteredByDoctors($Ids, $lines, $pmDoctors, $filterBy)
                ->pluck('account_dr_id')->count();


            $this->coverageAnalyzer->addTo(AnalyzerCoverage::PH_DOCTOR_COVERAGE, $phCoverage, 0);
            $this->coverageAnalyzer->addTo(AnalyzerCoverage::AM_DOCTOR_COVERAGE, $amCoverage, 0);
            $this->coverageAnalyzer->addTo(AnalyzerCoverage::PM_DOCTOR_COVERAGE, $pmCoverage, 0);
            return;
        }

        $phAccounts = $count['accounts']->where("acc_shift_id", 3);
        $amAccounts = $count['accounts']->where("acc_shift_id", 1);
        $pmAccounts = $count['accounts']->where("acc_shift_id", 2);

        $phCoverage = $this->getVisitsFilteredByAccounts($Ids, $lines, $phAccounts, $filterBy)
            ->pluck('account_dr_id')->count();
        $amCoverage = $this->getVisitsFilteredByAccounts($Ids, $lines, $amAccounts, $filterBy)
            ->pluck('account_dr_id')->count();
        $pmCoverage = $this->getVisitsFilteredByAccounts($Ids, $lines, $pmAccounts, $filterBy)
            ->pluck('account_dr_id')->count();

        $this->coverageAnalyzer->addTo(AnalyzerCoverage::PH_ACCOUNT_COVERAGE, $phCoverage, 0);
        $this->coverageAnalyzer->addTo(AnalyzerCoverage::AM_ACCOUNT_COVERAGE, $amCoverage, 0);
        $this->coverageAnalyzer->addTo(AnalyzerCoverage::PM_ACCOUNT_COVERAGE, $pmCoverage, 0);

    }

    private function calculate($holder, array $lines, int $shift, string $filterBy): float
    {
        $user = new User();
        $user->id = $holder->user_id;
        $actuals = (new ActualService)->getActuals(
            $user,
            $filterBy,
            $this->from,
            $this->to,
            [$shift],
            $lines
        );

        $actualCounts = $actuals->where('acc_shift_id', $shift)->count();

        $vacations = $this->getVacationCount($holder->user_id, $shift);
        // $workingDays = CallRate::workingDays($this->from, $this->to, $shift);
        $officeWorkDays = $this->getOfficeWorkDays($holder->user_id, $shift);
        $net_days = $workingDays - $vacations - $officeWorkDays;
        $dailyTarget = $this->getDailyTarget($holder->div_id, $shift);
        $monthlyTarget = $dailyTarget * $net_days;

        return ($monthlyTarget ? $actualCounts / $monthlyTarget : $monthlyTarget) * 100;
    }

    function calculateCallRate($holder, array $lines): void
    {
        $filterBy = "users.id";
        $amCallRate = $this->calculate($holder, $lines, 1, $filterBy);
        $pmCallRate = $this->calculate($holder, $lines, 2, $filterBy);
        $phCallRate = $this->calculate($holder, $lines, 3, $filterBy);
        $this->callRateAnalyzer->addTo(AnalyzerCallRate::AM_CALL_RATE, $amCallRate);
        $this->callRateAnalyzer->addTo(AnalyzerCallRate::PM_CALL_RATE, $pmCallRate);
        $this->callRateAnalyzer->addTo(AnalyzerCallRate::PH_CALL_RATE, $phCallRate);
    }


    private function getVacationCount(int $userId, $shift): int
    {
        $user = new User();
        $user->id = $userId;
        $months = [$this->from->format('m'), $this->to->format('m')];
        $year = $this->from->format('Y');
        return Vacation::getVacationsDatePerPeriod(
            $user,
            $this->from,
            $this->to,
            $months,
            $year,
            [$shift]
        );
    }

    private function getOfficeWorkDays(int $userId, int $shift): int
    {
        $ow = OwActualVisit::where('user_id', $userId)->whereBetween('date', [$this->from, $this->to]);
        $ow = $ow->where(fn($q) => $q->where('shift_id', $shift)->orWhereNull('shift_id'));

        return $ow->get()->filter(function ($ow) use ($shift) {
            return !OffDay::isOffDay($this->from, $this->to, $ow->date, $shift);
        })->count();
    }

    private function getDailyTarget($divisionId, $shift)
    {
        $months = [
            $this->from->format('m'),
            $this->to->format('m')
        ];
        $year = $this->from->format('Y');

        $callRate = CallRate::where('division_id', $divisionId)
            ->whereBetween(DB::raw("(DATE_FORMAT(crm_call_rates.date,'%m'))"), $months)
            ->whereYear('call_rates.date', $year);

        $callRate = $callRate->where('shift_id', $shift);

        return $callRate->first()?->call_rate ?? 0;
    }

    private function calculateFrequency(Collection $doctors, int $userIdOrDivId, string $column): void
    {

        $months = [$this->from->format('m'), $this->to->format('m')];
        $year = $this->from->format('Y');
        $actuals = $this->actuals(
            $userIdOrDivId,
            $column,
            $doctors->pluck('doctor_id')->toArray(),
            $months,
            $year
        );
        $doctors->each(function ($doctor) use (
            $months,
            $year,
            $actuals
        ) {
            $frequency = $doctor->frequency;
            $actual = $actuals->where('account_dr_id', $doctor->id)->count();
            if ($frequency === 0) {
                $this->analyzerFrequencyService
                    ->addTo(
                        AnalyzerFrequency::BELOW_FREQUENCY,
                        1
                    );
                return;
            }
            if ($frequency === $actual)
                $this->analyzerFrequencyService
                    ->addTo(
                        AnalyzerFrequency::MEET_FREQUENCY,
                        1
                    );
            if ($frequency > $actual)
                $this->analyzerFrequencyService
                    ->addTo(
                        AnalyzerFrequency::BELOW_FREQUENCY,
                        1
                    );
            if ($frequency < $actual)
                $this->analyzerFrequencyService
                    ->addTo(
                        AnalyzerFrequency::ABOVE_FREQUENCY,
                        1
                    );

        });

    }

    private function frequencyType($doctor, array $months, $year): Collection
    {
        $freqType = OtherSetting::where("key", "type_of_frequency")->value("value");
        return match ($freqType) {
            1 => ClassFrequency::select([
                'id',
                'line_id',
                'class_id',
                'class_frequencies.frequency as frequency',
                'class_frequencies.date as date'
            ])
                ->where('class_frequencies.class_id', $doctor['doc_class_id'])
                ->whereBetween(DB::raw("(DATE_FORMAT(crm_class_frequencies.date,'%m'))"), $months)
                ->whereYear('class_frequencies.date', $year)
                ->groupBy('id', 'class_frequencies.frequency')->get(),
            2 => DoctorFrequency::select([
                'id',
                'line_id',
                'doctor_id',
                'account_id',
                'date',
                'doctor_frequencies.frequency as frequency',
                'doctor_frequencies.date as date'
            ])
                ->where('doctor_frequencies.doctor_id', $doctor['id'])
                ->where('doctor_frequencies.account_id', $doctor['account_id'])
                ->whereBetween(DB::raw("(DATE_FORMAT(crm_doctor_frequencies.date,'%m'))"), $months)
                ->whereYear('doctor_frequencies.date', $year)
                ->groupBy('id', 'doctor_frequencies.frequency')->get(),
            3 => SpecialityFrequency::select([
                'id',
                'line_id',
                'speciality_id',
                'speciality_frequencies.frequency as frequency',
                'speciality_frequencies.date as date'
            ])
                ->where('speciality_frequencies.speciality_id', $doctor['speciality_id'])
                ->whereBetween(DB::raw("(DATE_FORMAT(crm_speciality_frequencies.date,'%m'))"), $months)
                ->whereYear('speciality_frequencies.date', $year)
                ->groupBy('id', 'speciality_frequencies.frequency')->get(),
            default => collect(),
        };
    }

    private function actuals(int $userIdOrDivId, string $column, array $doctors, array $month, $year): Collection
    {

        $data = ActualVisit::select([
            'actual_visits.id as id',
            'actual_visits.visit_date',
            'actual_visits.account_dr_id',
            DB::raw("DATE_FORMAT(crm_actual_visits.created_at,'%Y-%m-%d %H:%s:%i') as date"),
            'specialities.id as speciality_id',
            'bricks.name as brick',
            'lines.name as line',
            'line_divisions.name as division',
            'users.fullname as user',
            'accounts.name as account',
            'accounts.id as account_id',
            'account_types.name as acc_type',
            DB::raw('IFNULL(crm_doctors.name,"") as doctor'),
            DB::raw('IFNULL(crm_doctors.id,"") as doctor_id'),
            DB::raw('IFNULL(group_concat(distinct crm_products.name),"") as product'),
            'specialities.name as speciality',
            'visit_types.name as type',
            'plan_visit_details.approval as status'
        ])
            ->leftJoin('lines', 'actual_visits.line_id', 'lines.id')
            ->leftJoin('line_divisions', 'actual_visits.div_id', 'line_divisions.id')
            ->leftJoin('users', 'actual_visits.user_id', 'users.id')
            ->leftJoin('accounts', 'actual_visits.account_id', 'accounts.id')
            ->leftJoin('bricks', 'actual_visits.brick_id', 'bricks.id')
            ->leftJoin('account_types', 'actual_visits.acc_type_id', 'account_types.id')
            ->leftJoin('doctors', 'actual_visits.account_dr_id', 'doctors.id')
            ->leftJoin('specialities', 'doctors.speciality_id', 'specialities.id')
            ->leftJoin('visit_types', 'actual_visits.visit_type_id', 'visit_types.id')
            ->leftJoin(
                'plan_visit_details',
                function ($join) {
                    $join->on('actual_visits.id', '=', 'plan_visit_details.visitable_id');
                    $join->where('plan_visit_details.visitable_type', 'App\ActualVisit');
                }
            )
            ->leftJoin('actual_visit_products', 'actual_visits.id', 'actual_visit_products.visit_id')
            ->leftJoin('products', 'actual_visit_products.product_id', 'products.id')
            ->where('actual_visits.deleted_at', '=', null)
            ->whereIntegerInRaw('actual_visits.account_dr_id', $doctors)
            ->where(fn($q) => $q->where('plan_visit_details.approval', 1)->orWhere('plan_visit_details.approval', null))
            ->where($column, $userIdOrDivId)
            ->whereBetween(DB::raw("(DATE_FORMAT(crm_actual_visits.visit_date,'%m'))"), $month)
            ->whereYear('visit_date', $year);

        return $data->groupBy('id', 'plan_visit_details.approval')->get();
    }


}
