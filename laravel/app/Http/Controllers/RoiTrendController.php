<?php

namespace App\Http\Controllers;

use App\LinkedParmaciesSetting;
use App\Models\CommercialRequest\CommercialRequest;
use App\Models\CommercialRequest\CommercialDoctor;
use App\Models\CommercialRequest\CommercialProduct;
use App\Models\LinkedPharmacy;
use App\Sale;
use App\SaleDetail;
use App\Product;
use App\Brand;
use App\Line;
use App\LineDivision;
use App\Brick;
use App\User;
use App\Account;
use App\Doctor;
use App\Exceptions\CrmException;
use App\Helpers\LogActivity;
use App\Http\Requests\RoiTrendRequest;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Carbon;

class RoiTrendController extends ApiController
{

    private function getCommercials($lineIds, $from, $to, $monthsBefore, $monthsAfter)
    {
        return CommercialRequest::select([
            'commercial_requests.id as id',
            'commercial_requests.created_at as insertion_date',
            'commercial_requests.to_date as to',
            'request_types.name as type',
            'users.name as employee',
            'users.id as user_id',
            'brands.id as brand_id',
            'brands.name as brand',
            // DB::raw('IFNULL(GROUP_CONCAT(DISTINCT crm_commercial_products.product_id), "") as commercial_product_ids'),
            DB::raw('IFNULL(GROUP_CONCAT(DISTINCT crm_brand_products.id), "") as brand_product_ids'),
            //  DB::raw('CAST(CONCAT("[", GROUP_CONCAT(DISTINCT crm_brand_products.id), "]") AS JSON) as brand_product_ids'),
            'accounts.id as account_id',
            'accounts.name as account',
            DB::raw('IFNULL(GROUP_CONCAT(DISTINCT crm_bricks.id), "") as brick_id'),
            DB::raw('IFNULL(GROUP_CONCAT(DISTINCT crm_bricks.name), "") as brick'),
        ])
            ->leftJoin('users', 'commercial_requests.user_id', 'users.id')
            ->leftJoin('request_types', 'commercial_requests.request_type_id', 'request_types.id')
            ->leftJoin('commercial_doctors', 'commercial_requests.id', 'commercial_doctors.request_id')
            ->leftJoin('doctors', 'commercial_doctors.doctor_id', 'doctors.id')
            ->leftJoin('commercial_products', 'commercial_requests.id', 'commercial_products.request_id')
            ->leftJoin('products', 'commercial_products.product_id', 'products.id')
            ->leftJoin('product_brands', function ($join) {
                $join->on('products.id', '=', 'product_brands.product_id')
                    ->where('product_brands.from_date', '<=', now())
                    ->where(function ($q) {
                        $q->whereNull('product_brands.to_date')->orWhere('product_brands.to_date', '>=', now());
                    });
            })
            ->leftJoin('brands', 'product_brands.brand_id', 'brands.id')
            // join all brand products, even if not used in commercial_products
            ->leftJoin('product_brands as brand_products_map', function ($join) {
                $join->on('brand_products_map.brand_id', '=', 'product_brands.brand_id')
                    ->where('brand_products_map.from_date', '<=', now())
                    ->where(function ($q) {
                        $q->whereNull('brand_products_map.to_date')
                            ->orWhere('brand_products_map.to_date', '>=', now());
                    });
            })
            ->leftJoin('products as brand_products', 'brand_products_map.product_id', 'brand_products.id')
            ->leftJoin('accounts', 'commercial_doctors.account_id', 'accounts.id')
            ->leftJoin('account_lines', function ($join) use ($lineIds) {
                $join->on('accounts.id', '=', 'account_lines.account_id')
                    ->where('account_lines.from_date', '<=', now())
                    ->whereIntegerInRaw('account_lines.line_id', $lineIds)
                    ->where(function ($q) {
                        $q->whereNull('account_lines.to_date')->orWhere('account_lines.to_date', '>=', now());
                    });
            })
            ->leftJoin('bricks', 'account_lines.brick_id', 'bricks.id')
            ->whereBetween('commercial_requests.created_at', [$from, $to])
            ->groupBy(
                'commercial_requests.id',
                'brands.id',
                'accounts.id',
                'users.id',
                'request_types.name',
                'brands.name',
                'accounts.name'
            )
            ->get();
    }

    /**
     * Generate ROI Trend Report
     */
    public function generateReport(RoiTrendRequest $request)
    {
        $roiFilter = $request->roiFilter;
        $from = Carbon::parse($roiFilter['fromDate'])->startOfDay();
        $to = Carbon::parse($roiFilter['toDate'])->endOfDay();
        // Get LinkedPharmaciesSetting values
        $monthsBefore = LinkedParmaciesSetting::where('key', 'number_of_month_befor')->value('value') ?? 2;
        $monthsAfter = LinkedParmaciesSetting::where('key', 'number_of_month_after')->value('value') ?? 3;
        // Build commercial requests query
        $commercialRequestsQuery = $this->getCommercials($roiFilter['lines'], $from, $to, $monthsBefore, $monthsAfter);
        // throw new CrmException($roiFilter);
        // throw new CrmException($commercialRequestsQuery);
        // Apply filters
        // if (!empty($lineIds)) {
        //     $commercialRequestsQuery->whereHas('user.lineDivisions.line', function ($q) use ($lineIds) {
        //         $q->whereIn('lines.id', $lineIds);
        //     });
        // }

        // if (!empty($typeIds)) {
        //     $commercialRequestsQuery->whereIn('request_type_id', $typeIds);
        // }

        // $reportData = $commercialRequestsQuery;

        // $reportData = [];
        $fields = [
            'id',
            'insertion_date',
            'account',
            'brick',
            'employee',
            'brand',
            'brand_product_ids'
            // 'brick_sales_before',
            // 'linked_pharm_before',
            // 'brick_sales_factor',
            // 'linked_pharm_factor'
        ];

        // // Add dynamic month columns
        // for ($i = 1; $i <= $monthsAfter; $i++) {
        //     $fields[] = "brick_sales_month_{$i}";
        //     $fields[] = "brick_sales_arrow_{$i}";
        //     $fields[] = "linked_pharm_month_{$i}";
        //     $fields[] = "linked_pharm_arrow_{$i}";
        // }

        // $fields = array_merge($fields, [
        //     'total_brick_sales_after',
        //     'total_linked_pharm_after',
        //     'brick_sales_trend',
        //     'linked_pharm_trend'
        // ]);

        // foreach ($commercialRequests as $request) {
        //     foreach ($request->doctors as $commercialDoctor) {
        //         foreach ($request->products as $commercialProduct) {
        //             $reportRow = $this->processCommercialRequestRow(
        //                 $request,
        //                 $commercialDoctor,
        //                 $commercialProduct,
        //                 $monthsBefore,
        //                 $monthsAfter
        //             );

        //             if ($reportRow) {
        //                 $reportData[] = $reportRow;
        //             }
        //         }
        //     }
        // }

        // LogActivity::addLog();

        return response()->json([
            'data' => $commercialRequestsQuery,
            'fields' => $fields,
        ]);
    }

    /**
     * Process individual commercial request row
     */
    private function processCommercialRequestRow($request, $commercialDoctor, $commercialProduct, $monthsBefore, $monthsAfter)
    {
        $doctor = $commercialDoctor->doctor;
        $account = Account::find($commercialDoctor->account_id);
        $product = $commercialProduct->product;
        $brand = $product->brand ?? null;

        if (!$doctor || !$account || !$product || !$brand) {
            return null;
        }

        // Get division and brick info
        $user = $request->user;
        $lineDivision = $user->lineDivisions->first();
        $division = $lineDivision ? $lineDivision->name : '';
        $line = $lineDivision ? $lineDivision->line->name : '';

        // Get brick from account lines
        $accountLine = DB::table('account_lines')
            ->join('line_divisions', 'account_lines.line_division_id', '=', 'line_divisions.id')
            ->join('bricks', 'account_lines.brick_id', '=', 'bricks.id')
            ->where('account_lines.account_id', $account->id)
            ->where('line_divisions.id', $lineDivision->id ?? 0)
            ->select('bricks.name as brick_name', 'bricks.id as brick_id', 'line_divisions.id as div_id')
            ->first();

        $brickName = $accountLine->brick_name ?? '';
        $brickId = $accountLine->brick_id ?? null;
        $divId = $accountLine->div_id ?? null;

        if (!$brickId || !$divId) {
            return null;
        }

        $createdAt = Carbon::parse($request->created_at);

        // Calculate before period sales
        $beforePeriodStart = $createdAt->copy()->subMonths($monthsBefore)->startOfMonth();
        $beforePeriodEnd = $createdAt->copy()->subMonth()->endOfMonth();

        $brickSalesBefore = $this->getBrickSales($brickId, $divId, $brand->id, $beforePeriodStart, $beforePeriodEnd);
        $linkedPharmBefore = $this->getLinkedPharmSales($account->id, $brand->id, $brickId, $divId, $beforePeriodStart, $beforePeriodEnd);

        // Calculate factors
        $brickSalesFactor = $monthsBefore > 0 ? $brickSalesBefore / $monthsBefore : 0;
        $linkedPharmFactor = $monthsBefore > 0 ? $linkedPharmBefore / $monthsBefore : 0;

        $row = [
            'id' => $request->id,
            'created_at' => $createdAt->format('Y-m-d'),
            'account_name' => $account->name,
            'division' => $division,
            'brick' => $brickName,
            'line' => $line,
            'employee' => $user->name,
            'doctor_name' => $doctor->name,
            'product_name' => $product->name,
            'brand_name' => $brand->name,
            'brick_sales_before' => number_format($brickSalesBefore, 2),
            'linked_pharm_before' => number_format($linkedPharmBefore, 2),
            'brick_sales_factor' => number_format($brickSalesFactor, 2),
            'linked_pharm_factor' => number_format($linkedPharmFactor, 2)
        ];

        // Calculate after period sales for each month
        $totalBrickSalesAfter = 0;
        $totalLinkedPharmAfter = 0;

        for ($i = 1; $i <= $monthsAfter; $i++) {
            $monthStart = $createdAt->copy()->addMonths($i - 1)->startOfMonth();
            $monthEnd = $createdAt->copy()->addMonths($i - 1)->endOfMonth();

            $brickSalesMonth = $this->getBrickSales($brickId, $divId, $brand->id, $monthStart, $monthEnd);
            $linkedPharmMonth = $this->getLinkedPharmSales($account->id, $brand->id, $brickId, $divId, $monthStart, $monthEnd);

            $totalBrickSalesAfter += $brickSalesMonth;
            $totalLinkedPharmAfter += $linkedPharmMonth;

            // Calculate arrows (trend indicators)
            $brickArrow = $this->getTrendArrow($brickSalesMonth, $brickSalesFactor);
            $linkedPharmArrow = $this->getTrendArrow($linkedPharmMonth, $linkedPharmFactor);

            $row["brick_sales_month_{$i}"] = number_format($brickSalesMonth, 2);
            $row["brick_sales_arrow_{$i}"] = $brickArrow;
            $row["linked_pharm_month_{$i}"] = number_format($linkedPharmMonth, 2);
            $row["linked_pharm_arrow_{$i}"] = $linkedPharmArrow;
        }

        $row['total_brick_sales_after'] = number_format($totalBrickSalesAfter, 2);
        $row['total_linked_pharm_after'] = number_format($totalLinkedPharmAfter, 2);
        $row['brick_sales_trend'] = $this->getTrendArrow($totalBrickSalesAfter, $brickSalesBefore);
        $row['linked_pharm_trend'] = $this->getTrendArrow($totalLinkedPharmAfter, $linkedPharmBefore);

        return $row;
    }

    /**
     * Get brick sales for specific brand, brick, division and date range
     */
    private function getBrickSales($brickId, $divId, $brandId, $startDate, $endDate)
    {
        return DB::table('sales_details')
            ->join('sales', 'sales_details.sale_id', '=', 'sales.id')
            ->join('products', 'sales.product_id', '=', 'products.id')
            ->where('sales_details.brick_id', $brickId)
            ->where('sales_details.div_id', $divId)
            ->where('products.brand_id', $brandId)
            ->whereBetween('sales_details.date', [$startDate, $endDate])
            ->sum('sales_details.value');
    }

    /**
     * Get linked pharmacy sales for specific account, brand, brick, division and date range
     */
    private function getLinkedPharmSales($accountId, $brandId, $brickId, $divId, $startDate, $endDate)
    {
        // Get linked pharmacies for this account
        $linkedPharmacies = LinkedPharmacy::where('account_id', $accountId)
            ->where('deleted_at', null)
            ->get();

        $totalSales = 0;

        foreach ($linkedPharmacies as $linkedPharmacy) {
            if ($linkedPharmacy->pharmable_type === 'App\Mapping') {
                // Get sales from mapping
                $sales = DB::table('sales_details')
                    ->join('sales', 'sales_details.sale_id', '=', 'sales.id')
                    ->join('products', 'sales.product_id', '=', 'products.id')
                    ->where('sales.mapping_id', $linkedPharmacy->pharmable_id)
                    ->where('sales_details.brick_id', $brickId)
                    ->where('sales_details.div_id', $divId)
                    ->where('products.brand_id', $brandId)
                    ->whereBetween('sales_details.date', [$startDate, $endDate])
                    ->sum('sales_details.value');

                // Apply ratio
                $totalSales += ($sales * $linkedPharmacy->ratio / 100);
            }
        }

        return $totalSales;
    }

    /**
     * Get trend arrow based on comparison
     */
    private function getTrendArrow($currentValue, $compareValue)
    {
        if ($compareValue == 0) {
            return $currentValue > 0 ? '↑' : '→';
        }

        $percentage = (($currentValue - $compareValue) / $compareValue) * 100;

        if ($percentage > 5) {
            return '↑'; // Up arrow for significant increase
        } elseif ($percentage < -5) {
            return '↓'; // Down arrow for significant decrease
        } else {
            return '→'; // Right arrow for stable/minor change
        }
    }

    /**
     * Show detailed data for specific row and column
     */
    public function showDetailedData(Request $request)
    {
        $requestId = $request->id;
        $column = $request->column;
        $listFilter = $request->listFilter;

        // Get the commercial request
        $commercialRequest = CommercialRequest::with([
            'doctors.doctor',
            'doctors.account',
            'products.product.brand',
            'user'
        ])->find($requestId);

        if (!$commercialRequest) {
            return response()->json(['data' => []]);
        }

        $detailedData = [];

        // Based on column, show different detailed information
        switch ($column) {
            case 'brick_sales_before':
            case (preg_match('/brick_sales_month_\d+/', $column) ? $column : null):
                $detailedData = $this->getBrickSalesDetails($commercialRequest, $column, $listFilter);
                break;

            case 'linked_pharm_before':
            case (preg_match('/linked_pharm_month_\d+/', $column) ? $column : null):
                $detailedData = $this->getLinkedPharmSalesDetails($commercialRequest, $column, $listFilter);
                break;

            default:
                $detailedData = $this->getGeneralDetails($commercialRequest, $column);
                break;
        }

        LogActivity::addLog();

        return response()->json(['data' => $detailedData]);
    }

    /**
     * Get brick sales details
     */
    private function getBrickSalesDetails($commercialRequest, $column, $listFilter)
    {
        // Implementation for brick sales details
        return [];
    }

    /**
     * Get linked pharmacy sales details
     */
    private function getLinkedPharmSalesDetails($commercialRequest, $column, $listFilter)
    {
        // Implementation for linked pharmacy sales details
        return [];
    }

    /**
     * Get general details
     */
    private function getGeneralDetails($commercialRequest, $column)
    {
        // Implementation for general details
        return [];
    }
}
