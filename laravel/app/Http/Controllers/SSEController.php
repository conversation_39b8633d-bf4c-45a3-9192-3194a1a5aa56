<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\StreamedResponse;

class SSEController extends Controller
{
    /**
     * The SSE endpoint for progress tracking
     *
     * @return \Symfony\Component\HttpFoundation\StreamedResponse
     */
    public function progressStream()
    {
        $response = new StreamedResponse(function () {
            try {
                // Disable output buffering
                if (ob_get_level()) {
                    ob_end_clean();
                }

                // Send initial connection message
                echo "event: connected\n";
                echo "data: " . json_encode(['connected' => true]) . "\n\n";

                // Ensure output is sent immediately
                if (function_exists('fastcgi_finish_request')) {
                    fastcgi_finish_request();
                } else {
                    flush();
                }
            
            // Get the user ID for user-specific progress tracking
            $userId = auth()->id();
            $cacheKey = "sse_progress_{$userId}";
            
            // Keep the connection open and check for progress updates
            $retry = 3000; // Retry interval in milliseconds
            
            while (true) {
                // Check if client closed the connection
                if (connection_aborted()) {
                    break;
                }
                
                // Get progress data from cache
                $progressData = Cache::get($cacheKey, []);
                
                // Send any new progress events
                if (!empty($progressData)) {
                    foreach ($progressData as $requestId => $data) {
                        // Only send if we haven't sent this event before
                        if (!isset($data['sent']) || !$data['sent']) {
                            echo "event: progress\n";
                            echo "data: " . json_encode([
                                'requestId' => $requestId,
                                'progress' => $data['progress'] ?? 0,
                                'message' => $data['message'] ?? '',
                                'isComplete' => $data['isComplete'] ?? false,
                                'timestamp' => now()->timestamp
                            ]) . "\n\n";
                            
                            // Mark as sent
                            $progressData[$requestId]['sent'] = true;
                            Cache::put($cacheKey, $progressData, now()->addMinutes(30));
                            
                            // If this event is marked as complete, remove it from the cache
                            if ($data['isComplete'] ?? false) {
                                unset($progressData[$requestId]);
                                Cache::put($cacheKey, $progressData, now()->addMinutes(30));
                            }

                            // Ensure output is sent immediately
                            if (function_exists('fastcgi_finish_request')) {
                                fastcgi_finish_request();
                            } else {
                                flush();
                            }
                        }
                    }
                }
                
                // Send retry directive
                echo "retry: {$retry}\n\n";

                // Ensure output is sent immediately
                if (function_exists('fastcgi_finish_request')) {
                    fastcgi_finish_request();
                } else {
                    flush();
                }
                
                // Sleep for a short time to prevent CPU overuse
                usleep(500000); // 500ms
            }
            } catch (\Exception $e) {
                Log::error('SSE Error: ' . $e->getMessage(), [
                    'exception' => $e,
                    'user_id' => auth()->id() ?? 'unauthenticated'
                ]);

                // Send error event
                echo "event: error\n";
                echo "data: " . json_encode(['error' => 'An error occurred']) . "\n\n";
            }
        });
        
        // Set response headers for SSE
        $response->headers->set('Content-Type', 'text/event-stream');
        $response->headers->set('Cache-Control', 'no-cache, no-store, must-revalidate');
        $response->headers->set('Pragma', 'no-cache');
        $response->headers->set('Expires', '0');
        $response->headers->set('Connection', 'keep-alive');
        $response->headers->set('X-Accel-Buffering', 'no'); // Disable buffering for Nginx
        $response->headers->set('Access-Control-Allow-Origin', '*');
        $response->headers->set('Access-Control-Allow-Headers', 'Cache-Control');
        
        return $response;
    }
    
    /**
     * Register a new progress tracking request
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function registerProgress(Request $request)
    {
        $requestId = $request->input('requestId', 'default');
        $userId = auth()->id();
        $cacheKey = "sse_progress_{$userId}";
        
        // Get existing progress data or initialize empty array
        $progressData = Cache::get($cacheKey, []);
        
        // Add new request with initial progress
        $progressData[$requestId] = [
            'progress' => 0,
            'message' => 'Starting...',
            'isComplete' => false,
            'sent' => false,
            'created_at' => now()->timestamp
        ];
        
        // Store in cache
        Cache::put($cacheKey, $progressData, now()->addMinutes(30));
        
        return response()->json(['success' => true]);
    }
    
    /**
     * Unregister a progress tracking request
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function unregisterProgress(Request $request)
    {
        $requestId = $request->input('requestId', 'default');
        $userId = auth()->id();
        $cacheKey = "sse_progress_{$userId}";
        
        // Get existing progress data
        $progressData = Cache::get($cacheKey, []);
        
        // Mark the request as complete
        if (isset($progressData[$requestId])) {
            $progressData[$requestId]['isComplete'] = true;
            $progressData[$requestId]['progress'] = 100;
            $progressData[$requestId]['message'] = 'Completed';
            $progressData[$requestId]['sent'] = false; // Ensure the completion event is sent
            
            // Store in cache
            Cache::put($cacheKey, $progressData, now()->addMinutes(30));
        }
        
        return response()->json(['success' => true]);
    }
    
    /**
     * Update progress for a specific request
     * This can be called from other parts of the application
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateProgress(Request $request)
    {
        $requestId = $request->input('requestId', 'default');
        $progress = $request->input('progress', 0);
        $message = $request->input('message', '');
        $isComplete = $request->input('isComplete', false);
        $userId = $request->input('userId', auth()->id());
        
        $cacheKey = "sse_progress_{$userId}";
        
        // Get existing progress data
        $progressData = Cache::get($cacheKey, []);
        
        // Update progress
        $progressData[$requestId] = [
            'progress' => $progress,
            'message' => $message,
            'isComplete' => $isComplete,
            'sent' => false, // Mark as not sent so it will be sent in the next cycle
            'updated_at' => now()->timestamp
        ];
        
        // Store in cache
        Cache::put($cacheKey, $progressData, now()->addMinutes(30));
        
        return response()->json(['success' => true]);
    }
}
