<?php

namespace App\Http\Controllers;

use App\Exceptions\CrmException;
use App\Services\AuthService;
use Illuminate\Support\Facades\DB;
use Illuminate\Http\Request;
use App\Http\Requests\UserRequest;
use App\Http\Requests\UserDetailsRequest;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Hash;
use Maatwebsite\Excel\Excel as ExcelType;
use App\Helpers\LogActivity;
use App\Helpers\ExcelImporter;
use App\Helpers\Image;
use App\Http\Requests\ImportRequest;
use App\Http\Requests\MailRequest;
use App\User;
use App\UserDetails;
use App\Role;
use Illuminate\Support\Facades\Auth;
use App\Imports\Updates\UsersImport as UpdatesUsersImport;
use App\LineDivisionUser;
use App\LineUser;
use App\Models\ScientificOffice;
use App\Models\UnhashedPassword;
use Illuminate\Support\Carbon;

class UsersController extends ApiController
{

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth:api');
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        /**@var User $user */
        $user = Auth::user();
        $fields = [];
        if ($user->hasRole('sub admin') || $user->hasRole('Gemstone Admin')) {
            $fields = [
                "id",
                "line",
                "division",
                "code",
                "full_name",
                "user_name",
                "registered",
                "history",
                "roles",
                "status",
                "actions"
            ];
        } else {
            $fields = [
                "id",
                "line",
                "division",
                "code",
                "full_name",
                "user_name",
                "password",
                "registered",
                "history",
                "roles",
                "status",
                "actions"
            ];
        }
        $users = DB::table('users')
            ->select(
                'users.id',
                DB::raw('IFNULL(group_concat(distinct crm_lines.name),"") as line'),
                DB::raw('IFNULL(group_concat(distinct crm_line_divisions.name),"") as division'),
                DB::raw('IFNULL(group_concat(distinct crm_unhashed_passwords.password),"") as password'),
                'users.fullname as full_name',
                'users.name as user_name',
                'users.email',
                DB::raw('IFNULL(crm_users.emp_code,"") as code'),
                'users.menuroles as roles',
                'users.status',
                'users.created_at as registered'
            )
            ->leftJoin('unhashed_passwords', 'users.id', 'unhashed_passwords.user_id')
            // ->leftJoin('line_users', 'users.id', 'line_users.user_id')
            ->leftJoin('line_users', function ($join) {
                $join->on('users.id', 'line_users.user_id')
                    ->whereNull('line_users.deleted_at')
                    ->where(fn($q) => $q->where('line_users.to_date', '>', (string)Carbon::now())
                        ->orWhere('line_users.to_date', null));
            })
            ->leftJoin('lines', function ($join) {
                $join->on('line_users.line_id', 'lines.id')->where(fn($q) => $q->where('lines.to_date', '>', (string)Carbon::now())
                    ->orWhere('lines.to_date', null));
            })
            ->leftJoin('line_users_divisions', function ($join) {
                $join->on('users.id', 'line_users_divisions.user_id')
                    ->whereNull('line_users_divisions.deleted_at')
                    ->where(fn($q) => $q->where('line_users_divisions.to_date', '>', (string)Carbon::now())
                        ->orWhere('line_users_divisions.to_date', null));
            })
            ->leftJoin('line_divisions', function ($join) {
                $join->on('line_users_divisions.line_division_id', 'line_divisions.id')
                    ->where('line_divisions.from_date', '<=', now())
                    ->whereNull('line_divisions.deleted_at')
                    ->where(fn($q) => $q->where('line_divisions.to_date', '>', (string)Carbon::now())
                        ->orWhere('line_divisions.to_date', null));
            })
            ->whereNull('users.deleted_at')
            ->where('users.is_hidden', 0)
            ->where(fn($q) => $q->where('users.id', 'Like', '%' . request('query') . '%')
                ->orWhere('users.emp_code', 'Like', '%' . request('query') . '%')
                ->orWhere('users.name', 'Like', '%' . request('query') . '%')
                ->orWhere('users.fullname', 'Like', '%' . request('query') . '%')
                ->orWhere('users.menuroles', 'Like', '%' . request('query') . '%')
                ->orWhere('users.status', 'Like', '%' . request('query') . '%')
                ->orWhere('unhashed_passwords.password', 'Like', '%' . request('query') . '%')
                ->orWhere('lines.name', 'Like', '%' . request('query') . '%')
                ->orWhere('line_divisions.name', 'Like', '%' . request('query') . '%')
                ->orWhere('users.created_at', 'Like', '%' . request('query') . '%'));
        if ($user->hasRole('sub admin') || $user->hasRole('Gemstone Admin')) {
            $users = $users->where('users.menuroles', 'NOT LIKE', '%admin%');
        }
        $users = $users->groupBy('id')->get();
        LogActivity::addLog();
        return $this->respond(['users' => $users, 'fields' => $fields]);
    }

    /**
     * Display the specified resource.
     *
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $user = DB::table('users')
            ->select(
                'users.id',
                'users.name',
                'users.email',
                'users.menuroles as roles',
                'users.image',
                'users.android_id as serial',
                'scientific_offices.name as office',
                'users.status',
                'users.hiring_date as hiring_date',
                'users.emp_code as emp_code',
                'users.is_vacant as is_vacant',
                'users.created_at as registered'
            )
            ->leftJoin('scientific_offices', 'users.office_id', 'scientific_offices.id')
            ->where('users.id', '=', $id)
            ->first();

        $userDetails = DB::table('user_details')
            ->select(
                'user_details.id',
                'user_details.user_id',
                'user_details.ssn',
                'user_details.dob',
                'user_details.mobile',
                'user_details.tel',
                'user_details.address',
                'user_details.gender'
            )
            ->leftJoin('users', 'user_details.user_id', '=', 'users.id')
            ->where('user_details.user_id', '=', $id)
            ->first();



        $userHistory = DB::table('line_users_divisions')
            ->select(
                'users.id as id',
                'users.name as user',
                'lines.name as line',
                'line_divisions.name as division',
                'line_users_divisions.from_date',
                DB::raw('IFNULL( crm_line_users_divisions.to_date,"") as to_date'),
            )
            ->leftJoin('users', 'line_users_divisions.user_id', '=', 'users.id')
            ->leftJoin('lines', 'line_users_divisions.line_id', '=', 'lines.id')
            ->leftJoin('line_divisions', 'line_users_divisions.line_division_id', '=', 'line_divisions.id')
            ->where('line_users_divisions.user_id', '=', $id)
            ->get();

        $userHistory = $userHistory->map(function ($history) {

            return [
                "id" => $history->id,
                "user" => $history->user,
                "line" => $history->line,
                "division" => $history->division,
                "from_date" => Carbon::parse($history->from_date)->format("Y-m-d"),
                "to_date" => ($history->to_date !== '') ? Carbon::parse($history->to_date)->format("Y-m-d") : '',
            ];
        });
        // throw new CrmException($userHistory);

        $model_id = $id;
        $model_type = User::class;
        LogActivity::addLog($model_id, $model_type);
        return response()->json(['user' => $user, 'userDetails' => $userDetails, 'historyDetails' => $userHistory]);
    }

    public function create()
    {
        $roles = Role::pluck('name', 'id')->toArray();
        $offices = ScientificOffice::select('id', 'name')->get();
        return response()->json(['status' => 'success', 'roles' => $roles, 'offices' => $offices]);
    }

    public function profileImage($id)
    {
        if ($id == 'null' || $id == null) {
            return response()->json(['url' => auth()->user()->url]);
        }

        return response()->json(['url' => User::findOrFail($id)->url]);
    }

    /**
     * @throws \Exception
     */
    public function store(UserRequest $request)
    {
        if (User::active()->count() >= config('app.users_limit')) {
            throw new \Exception("You have exceeding the limit of active users. please contact your service provider to upgrade your service plan.");
        }
        // throw new CrmException($request->office_id);
        $user = User::create([
            'name' => $request->input('name'),
            'fullname' => $request->input('fullname'),
            'menuroles' => $request->input('menuroles'),
            'status' => $request->input('status'),
            'android_id' => $request->input('serial'),
            'hiring_date' => $request->input('hiringDate'),
            'emp_code' => $request->input('empCode'),
            'office_id' => $request->office_id == "null" ? null : $request->office_id,
            'email' => $request->input('email'),
            'personal_email' => $request->input('personal_email'),
            'password' => Hash::make($request->password),
            'api_token' => Str::random(10),
        ]);
        UnhashedPassword::create([
            'user_id' => $user->id,
            'password' => $request->password
        ]);



        if ($request->hasFile('image')) {
            // saving image
            $image = new Image($request->file('image'), 'users/' . $user->id . '/profile');
            $path = $image->save();
            $user->image = $path;
            $user->save();
        }

        LogActivity::addLog($user->id, User::class);

        $roles = explode(',', $request->input('menuroles'));

        foreach ($roles as $role) {
            $user->assignRole($role);
        }

        return response()->json(['status' => 'success', 'user_id' => $user->id]);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $user = DB::table('users')
            ->select(
                'users.id',
                'users.name',
                'users.fullname',
                'users.email',
                'users.personal_email',
                'users.menuroles',
                'users.android_id',
                'users.office_id',
                'users.status',
                'users.is_vacant',
                'users.hiring_date',
                DB::raw("DATE_FORMAT(crm_users.hiring_date,'%Y-%m-%d') as hiring_date"),
                'users.image',
                'users.emp_code',
                'unhashed_passwords.password as password'
            )
            ->leftJoin('unhashed_passwords', 'users.id', 'unhashed_passwords.user_id')
            ->leftJoin('scientific_offices', 'users.office_id', 'scientific_offices.id')
            ->where('users.id', '=', $id)
            ->first();

        $roles = Role::pluck('name', 'id')->toArray();

        $url = User::where('id', $id)->first()->url;

        $userDetails = DB::table('user_details')
            ->select(
                'user_details.id',
                'user_details.user_id',
                'user_details.ssn',
                'user_details.dob',
                'user_details.mobile',
                'user_details.tel',
                'user_details.address',
                'user_details.gender'
            )
            ->leftJoin('users', 'user_details.user_id', '=', 'users.id')
            ->where('user_details.user_id', '=', $id)
            ->first();
        $model_id = $id;
        $model_type = User::class;
        LogActivity::addLog($model_id, $model_type);


        $userLineDivisions = DB::table('line_users_divisions')
            ->select(
                'line_users_divisions.id',
                'line_users_divisions.user_id',
                'users.fullname as employee',
                'line_users_divisions.line_division_id',
                'line_users_divisions.line_id',
                DB::raw('DATE_FORMAT(crm_line_users_divisions.from_date, "%Y-%m-%d") as from_date'),
                DB::raw('IFNULL(DATE_FORMAT(crm_line_users_divisions.to_date, "%Y-%m-%d"),"") as to_date'),
                DB::raw('IFNULL(group_concat(distinct crm_line_divisions.name),"") as division'),
                DB::raw('IFNULL(group_concat(distinct crm_lines.name),"") as line'),

            )
            ->leftJoin('users', 'line_users_divisions.user_id', '=', 'users.id')
            ->leftJoin('lines', 'line_users_divisions.line_id', '=', 'lines.id')
            ->leftJoin('line_divisions', 'line_users_divisions.line_division_id', '=', 'line_divisions.id')
            ->where('line_users_divisions.user_id', '=', $id)
            ->where('line_users_divisions.deleted_at', null)
            ->groupBy('line_users_divisions.id')
            ->get();
        $offices = ScientificOffice::select('id', 'name')->get();
        return response()->json(['offices' => $offices, 'user' => $user, 'roles' => $roles, 'url' => $url, 'userLineDivision' => $userLineDivisions, 'userDetails' => $userDetails]);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function update(UserRequest $request, $id)
    {
        $user = User::find($id);
        if (
            User::active()->count() - 1 >= config('app.users_limit')
            && $user->status !== $request->input('status') && $request->input('status') == 'active'
        ) {

            throw new \Exception("You have exceeding the limit of active users. please contact your service provider to upgrade your service plan.");
        }
        $user->name = $request->input('name');
        $user->fullname = $request->input('fullname');
        $user->menuroles = implode(',', $request->input('menuroles'));
        $user->status = $request->input('status');
        $user->hiring_date = $request->input('hiringDate');
        $user->emp_code = $request->input('empCode');
        $user->is_vacant = $request->input('is_vacant');
        $user->android_id = $request->input('serial');
        $user->office_id = $request->input('office_id');
        $user->email = $request->input('email');
        $user->personal_email = $request->input('personal_email');
        $user->image = $request->input('image');



        // password is always sent from front. The old condition will be always true so I added truthy condition for password
        if ($request->has('password') && $request->password) {
            $userWithUnHashedPass = UnhashedPassword::where('user_id', $id)->first();
            $userWithUnHashedPass->password = $request->password;
            $userWithUnHashedPass->save();
            $user->password = Hash::make($request->password);
        }
        $user->api_token = Str::random(10);
        $user->save();
        $user->syncRoles(explode(',', $user->menuroles));
        if ($user->status == 'Inactive') {
            DB::table('oauth_access_tokens')->where('user_id', $user->id)->delete();
        }
        $model_id = $id;
        $model_type = User::class;

        LogActivity::addLog($model_id, $model_type);
        return response()->json(['status' => 'success']);
    }

    public function updateImage(Request $request, $id)
    {
        $user = User::find($id);
        $model_id = $id;
        $model_type = User::class;
        if ($request->hasFile('image')) {
            //     $user->image = saveImage($request->file('image'), 'users');
            $image = new Image($request->file('image'), 'users/' . $id . '/profile');
            $path = $image->save();
            $user->image = $path;
            $user->save();
        }
        return response()->json(['status' => 'success']);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        $user = User::find($id);
        LineUser::where("user_id", $id)->delete();
        LineDivisionUser::where("user_id", $id)->delete();
        // if ($user) {
        //     if ($lineUser > 0) {
        //         return response()->json(['statusText' => 'failed'], 422);
        //     }
        $user->details()->delete();
        $user->delete();
        // }
        $model_id = $id;
        $model_type = User::class;

        LogActivity::addLog($model_id, $model_type);
        return response()->json(['status' => 'success']);
    }

    public function import(ImportRequest $request)
    {
        User::import($request);
        return $this->respondSuccess();
    }

    public function updateByImport(ImportRequest $request)
    {
        User::import(
            request: $request,
            update: true
        );

        return $this->respondSuccess();
    }

    public function exportusers()
    {
        return User::export(ExcelType::XLSX);
    }

    public function exportcsv()
    {
        return User::export(ExcelType::CSV);
    }

    public function exportpdf()
    {
        $users = User::where('deleted_at', null)->get();
        return User::exportPdf($users);
    }

    public function sendmail(MailRequest $request)
    {
        $users = User::where('deleted_at', null)->get();
        return User::sendMail($request, $users);
    }

    public function storeUserDetails(UserDetailsRequest $request)
    {
        $userDetails = new UserDetails();
        $userDetails->user_id = $request->input('user_id');
        $userDetails->dob = $request->input('dob');
        $userDetails->ssn = $request->input('ssn');
        $userDetails->mobile = $request->input('mobile');
        $userDetails->tel = $request->input('tel');
        $userDetails->address = $request->input('address');
        $userDetails->gender = $request->input('gender');
        $userDetails->save();
        $model_id = $userDetails->id;
        $model_type = UserDetails::class;

        LogActivity::addLog($model_id, $model_type);

        return response()->json(['status' => 'success']);
    }

    public function storeUserLineDivision(Request $request)
    {
        DB::transaction(function () use ($request) {
            $isExistLineUser = LineUser::where('user_id', $request->user_id)
                ->where('line_id', $request->line)
                ->where('from_date', '<=', (string)Carbon::now())
                ->where(fn($q) => $q->where('to_date', '>=', (string)Carbon::now())
                    ->orWhere('to_date', null))->exists();
            if (!$isExistLineUser) {
                $userLine = new LineUser();
                $userLine->user_id = $request->input('user_id');
                $userLine->line_id = $request->input('line');
                $userLine->from_date = $request->input('from_date');
                $userLine->to_date = $request->input('to_date');
                $userLine->save();
                $model_id = $userLine->id;
                $model_type = LineUser::class;
                LogActivity::addLog($model_id, $model_type);
            }

            foreach ($request->input('division') as $division) {
                $isExistLineUserDivision = LineDivisionUser::where('user_id', $request->user_id)
                    ->where('line_id', $request->line)
                    ->where('line_division_id', $division)
                    ->where('from_date', '<=', (string)Carbon::now())
                    ->where(fn($q) => $q->where('to_date', '>=', Carbon::parse($request->from_date)->toDateString() ??  (string)Carbon::now())
                        ->orWhere('to_date', null))->exists();
                // throw new CrmException($isExistLineUserDivision);
                if (!$isExistLineUserDivision) {
                    $userLineDivision = new LineDivisionUser();
                    $userLineDivision->user_id = $request->input('user_id');
                    $userLineDivision->line_id = $request->input('line');
                    $userLineDivision->line_division_id = $division;
                    $userLineDivision->from_date = $request->input('from_date');
                    $userLineDivision->to_date = $request->input('to_date');
                    $userLineDivision->save();
                    $model_id = $userLineDivision->id;
                    $model_type = LineDivisionUser::class;
                    LogActivity::addLog($model_id, $model_type);
                }
            }
        });
        $lineUserDivsions = LineDivisionUser::where('user_id', $request->user_id)->with(['linedivision.line'])->get()->map(function ($lineUserDivsion) {
            return [
                'id' => $lineUserDivsion->id,
                'line_id' => $lineUserDivsion->line_id,
                'user_id' => $lineUserDivsion->user_id,
                'employee' => $lineUserDivsion->user?->fullname ?? '',
                'line' => $lineUserDivsion->linedivision->line->name ?? '',
                'line_division_id' => $lineUserDivsion->line_division_id ?? '',
                'division' => $lineUserDivsion->linedivision->name ?? '',
                'from_date' => Carbon::parse($lineUserDivsion->from_date)->toDateString() ?? '',
                'to_date' => $lineUserDivsion->to_date ? Carbon::parse($lineUserDivsion->to_date)->toDateString() : '',
            ];
        });
        return response()->json(['data' => $lineUserDivsions]);
    }


    public function getEditedUserLineDivision(LineDivisionUser $lineDivisionUser)
    {
        return response()->json(['lineDivisionUser' => $lineDivisionUser]);
    }

    public function showEditUserDetails($id)
    {

        $model_id = $id;
        $model_type = UserDetails::class;
        LogActivity::addLog($model_id, $model_type);
    }

    public function updateUserDetail(UserDetailsRequest $request, $id)
    {
        $userDetails = UserDetails::find($id);
        $userDetails->user_id = $request->input('user_id');
        $userDetails->dob = $request->input('dob');
        $userDetails->ssn = $request->input('ssn');
        $userDetails->mobile = $request->input('mobile');
        $userDetails->tel = $request->input('tel');
        $userDetails->address = $request->input('address');
        $userDetails->gender = $request->input('gender');

        $userDetails->save();


        $model_id = $id;
        $model_type = UserDetails::class;

        LogActivity::addLog($model_id, $model_type);

        return response()->json(['status' => 'success']);
    }

    public function updateUserLineDivisions(Request $request)
    {
        DB::transaction(function () use ($request) {
            if ($request->updateLineUser) {
                LineUser::where('user_id', $request->input('user_id'))
                    ->where('line_id', $request->input('line_id'))
                    ->where('from_date', '<=',  (string)Carbon::now())
                    ->where(fn($q) => $q->where('to_date', '>=', (string)Carbon::now())
                        ->orWhere('to_date', null))
                    ->update([
                        'from_date' => $request->from_date,
                        'to_date' => $request->to_date,
                    ]);
            }

            LineDivisionUser::find($request->lineDivisionUserId)
                ->update([
                    'from_date' => $request->from_date,
                    'to_date' => $request->to_date,
                ]);
            LogActivity::addLog($request->lineDivisionUserId, LineDivisionUser::class);
        });

        $lineUserDivsions = LineDivisionUser::where('user_id', $request->user_id)
            ->with(['linedivision.line'])->get()->map(function ($lineUserDivsion) {
                return [
                    'id' => $lineUserDivsion->id,
                    'line_id' => $lineUserDivsion->line_id,
                    'user_id' => $lineUserDivsion->user_id,
                    'line_division_id' => $lineUserDivsion->line_division_id,
                    'line' => $lineUserDivsion->linedivision->line->name,
                    'employee' => $lineUserDivsion->user?->fullname ?? '',
                    'division' => $lineUserDivsion->linedivision->name,
                    'from_date' => Carbon::parse($lineUserDivsion->from_date)->toDateString(),
                    'to_date' => $lineUserDivsion->to_date ? Carbon::parse($lineUserDivsion->to_date)->toDateString() : '',
                ];
            });
        return $this->respond($lineUserDivsions);
    }

    public function destroyUserLineDivisions(Request $request)
    {

        $userLine = LineUser::where('user_id', $request->input('user_id'))->where('line_id', $request->input('line_id'))->first();
        LogActivity::addLog($userLine->id, LineUser::class);

        $userLine = $userLine->delete();

        $userLineDivision = LineDivisionUser::where('user_id', $request->input('user_id'))->where('line_division_id', $request->input('line_division_id'))->first();
        LogActivity::addLog($userLineDivision->id, LineDivisionUser::class);
        $userLineDivision = $userLineDivision->delete();
        return response()->json(['status' => 'success']);
    }

    public function changePassword(Request $request)
    {
        $request->validate([
            'oldPassword' => 'required',
            'newPassword' => 'required|string|min:6|confirmed',
        ]);
        /**@var User */
        $user = Auth::user();

        // if (!Hash::check($request['oldPassword'], $user->password)) {
        //     return response()->json(['statusText' => 'old password does not match.'], 422);
        // }

        $userWithUnHashedPass = UnhashedPassword::where('user_id', $user->id)->first();
        // throw new CrmException($userWithUnHashedPass,1);
        $userWithUnHashedPass->password = $request['newPassword'];
        $userWithUnHashedPass->save();
        $user->password = Hash::make($request['newPassword']);

        $user->save();

        $id = $user->id;
        $model_id = $id;
        $model_type = User::class;

        LogActivity::addLog($model_id, $model_type);
        return response()->json(['status' => 'success']);
    }

    public function changeProfilePic(Request $request)
    {
        $request->validate([
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif,svg',
        ]);
        /**@var User */
        $user = Auth::user();
        if ($request->hasFile('image')) {
            $image = new Image($request->file('image'), 'users/' . $user->id . '/profile');
            $path = $image->save();
            $user->image = $path;
            $user->save();
        }
        $id = $user->id;
        $model_id = $id;
        $model_type = User::class;

        LogActivity::addLog($model_id, $model_type);
        return response()->json(['status' => 'success', 'user' => $user]);
    }

    public function getPermissions($id)
    {
        $user = User::find($id) ?? Auth::user();

        return $this->respond($user->getAllPermissions());
    }


    public function inPersonate(User $user)
    {
        $token = AuthService::login($user);

        return response()->json(['status' => 'success', 'token' => $token, 'user' => $user]);
    }

    public function userHistory(Request $request)
    {
        $user_id = $request->id;
        return $this->respond(User::where('id', $user_id)->with(['allLineDivisions.line'])->get()->map(function ($user) {
            return $user->allLineDivisions->map(function ($division) {
                return [
                    'id' => $division->pivot?->id ?? '',
                    'line' => optional($division->line)->name ?? '',
                    'division' => $division->name ?? '',
                    'color' => $division->DivisionType->color ?? '',
                    'from' => Carbon::parse($division->pivot->from_date)->toDateString() ?? '',
                    'to' => $division->pivot->to_date ? Carbon::parse($division->pivot->to_date)->toDateString() : '',
                ];
            });
        })->flatten(1));
    }
}
