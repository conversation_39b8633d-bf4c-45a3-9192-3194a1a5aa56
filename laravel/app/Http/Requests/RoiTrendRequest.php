<?php

namespace App\Http\Requests;

use App\Exceptions\CrmException;
use Illuminate\Foundation\Http\FormRequest;
use App\Helpers\CrmExcelDate;

class RoiTrendRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        // throw new CrmException($this->all());
        return [
            'roiFilter.lines' => 'required|array|min:1',
            'roiFilter.lines.*' => 'integer|exists:lines,id', // adjust table & column if needed

            'roiFilter.approval' => 'required|integer|in:1,2,3,4,5',

            'roiFilter.payments' => 'required|array|min:1',
            'roiFilter.payments.*' => 'integer|exists:payment_methods,id', // adjust

            'roiFilter.products' => 'nullable|array',
            'roiFilter.products.*' => 'integer|exists:products,id', // adjust

            'roiFilter.types' => 'required|array|min:1',
            'roiFilter.types.*' => 'integer|in:1,2,3,4,5,6,7,8,9,10,11,12,13,14,15',

            'roiFilter.fromDate' => 'required|date|before_or_equal:roiFilter.toDate',
            'roiFilter.toDate' => 'required|date|after_or_equal:roiFilter.fromDate',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'fromDate.required' => 'From date is required.',
            'fromDate.date' => 'From date must be a valid date.',
            'toDate.required' => 'To date is required.',
            'toDate.date' => 'To date must be a valid date.',
            'toDate.after_or_equal' => 'To date must be after or equal to from date.',
            'line_id.array' => 'Line selection must be an array.',
            'line_id.*.integer' => 'Each line ID must be an integer.',
            'line_id.*.exists' => 'Selected line does not exist.',
            'approval_id.in' => 'Approval type must be one of: all, approved, pending, rejected.',
            'payment_id.array' => 'Payment selection must be an array.',
            'payment_id.*.integer' => 'Each payment ID must be an integer.',
            'payment_id.*.exists' => 'Selected payment method does not exist.',
            'product_id.array' => 'Product selection must be an array.',
            'product_id.*.integer' => 'Each product ID must be an integer.',
            'product_id.*.exists' => 'Selected product does not exist.',
            'type_id.array' => 'Type selection must be an array.',
            'type_id.*.integer' => 'Each type ID must be an integer.',
            'type_id.*.exists' => 'Selected request type does not exist.',
        ];
    }
}
