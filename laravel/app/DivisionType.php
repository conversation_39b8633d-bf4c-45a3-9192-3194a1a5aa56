<?php

namespace App;

use App\Models\Coaching\Evaluator;
use App\Traits\ModelExportable;
use App\Traits\ModelImportable;
use App\Traits\PdfExportable;
use App\Traits\SendMail;
use App\Traits\SortableModel;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;

class DivisionType extends Model
{
    //
    use SoftDeletes;
    use ModelImportable;
    use ModelExportable;
    use SendMail;
    use PdfExportable;
    use SortableModel;

    protected $guard_name = 'api';

    protected $table = 'division_types';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'parent_id',
        'name',
        'notes',
        'sort',
        'role_id',
        'last_level',
        'target_days',
        'single_target_days',
        'double_target_days',
        'padding',
        'file_id',
        'level'
    ];

    protected $casts = [
        'deleted_at' => 'datetime',
    ];

    // protected $appends =['typeCount'];

    public function logActivities()
    {
        return $this->morphMany('App\LogActivity', 'loggable');
    }


    public function linedivisiontypes()
    {
        return $this->hasMany(LineDivisionType::class, 'divisiontype_id');
    }

    public function parent()
    {
        return $this->belongsTo(DivisionType::class, 'parent_id');
    }

    public function child()
    {
        return $this->hasOne(DivisionType::class, 'parent_id');
    }

    public function positions()
    {
        $positions = new Collection();
        foreach ($this->positionManagers as $value) {
            $positions->push($value->first()->position);
        }
        return $positions;
    }

    public function positionManagers()
    {
        return $this->morphMany(PositionManager::class, 'managerable');
    }

    public function approvable()
    {
        return $this->morphMany(Approvable::class, 'approvable');
    }

    public function planable()
    {
        return $this->morphMany(Planable::class, 'planable');
    }

    public function lineApprovables(int $id)
    {
        return $this->approvable()->whereLineId($id);
    }

    public function linePlanable(int $id)
    {
        return $this->planable()->whereLineId($id);
    }

    public function isLastLevel()
    {
        return $this->last_level == 1;
    }

    public function lines()
    {
        return $this->belongsToMany(Line::class, 'line_division_types', 'line_id', 'divisiontype_id')->withPivot('from_date', 'to_date');
    }

    public function divisions(?string $from = null, ?string $to = null): HasMany
    {
        $fromDate = Carbon::parse($from)->toDateString() ?? (string)Carbon::now();
        $toDate = Carbon::parse($to)->toDateString() ?? (string)Carbon::now();
        return $this->hasMany(LineDivision::class, 'division_type_id')
            ->where('from_date', '<=', $fromDate)
            ->where(fn($q) => $q->where('to_date', '=', null)->orWhere('to_date', '>=', $toDate));
    }

    public function childrens()
    {
        return $this->hasMany(DivisionType::class, 'parent_id');
    }

    /**
     * Get the divisionType's evaluator.
     */
    public function evaluator()
    {
        return $this->morphOne(Evaluator::class, 'evaluatorable');
    }

    public function descendants()
    {
        $descendants = collect([]);
        foreach ($this->childrens as $child) {
            $descendants->push($child);
            $descendants = $descendants->merge($child->descendants());
        }
        return $descendants;
    }

    public function ancestors()
    {
        $ancestors = collect([]);
        $child = $this->child;
        while ($child) {
            $ancestors->push($child);
            $child = $child->child;
        }
        return $ancestors;
    }

    public function role(): BelongsTo
    {
        return $this->belongsTo(Role::class);
    }
}
