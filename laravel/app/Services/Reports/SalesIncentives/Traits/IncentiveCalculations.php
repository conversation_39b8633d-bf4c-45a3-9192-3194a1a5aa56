<?php

namespace App\Services\Reports\SalesIncentives\Traits;

use App\Models\IncentiveMapping;
use App\Product;
use App\Role;
use App\Services\Enums\KPITypes;
use App\Services\Sales\SalesIncentiveHolder; // Required for type hinting in calculateKpiValue
use App\Services\Sales\Strategies\KpiCalculation\DefaultKpiStrategy;
use App\Services\Sales\Strategies\KpiCalculation\ManagerialKpiStrategy;
use App\Services\Sales\Strategies\KpiCalculation\KpiCalculationStrategy;
use Illuminate\Support\Facades\Log;

/**
 * Trait IncentiveCalculations
 *
 * Provides common methods for calculating sales incentives, including product values based on achievement
 * and KPI-based incentive values using a strategy pattern. This trait is typically used within
 * classes that have access to incentive configurations, such as SalesIncentiveHolder.
 *
 * It relies on methods like `getLastIncentive`, `getIncentiveOfAchievement`, `getPostKpi`,
 * `getKpiIncentiveAboveMR`, and `getKpiIncentive` which are expected to be implemented by the
 * class using this trait (e.g., SalesIncentiveHolder).
 */
trait IncentiveCalculations
{
    /**
     * Retrieves the incentive value for a given achievement percentage and role.
     * It considers rounding the achievement and checks against the last available incentive rule.
     *
     * @param float $achievementValues The raw achievement percentage.
     * @param int|null $role_id The role ID for which the incentive is being calculated.
     * @return float The calculated incentive value (typically a quarter value).
     */
    public function getIncentivePerAchievement(float $achievementValues, $role_id): float
    {
        $roundedAchievement = round($achievementValues);
        // Assuming $this has a method getLastIncentive, typically from SalesIncentiveHolder
        $lastIncentive = $this->getLastIncentive($role_id);

        if ($lastIncentive && $roundedAchievement > $lastIncentive->achievement_rule) {
            return $lastIncentive->quarter_value ?? 0.00;
        }
        // Assuming $this has a method getIncentiveOfAchievement
        return $this
            ->getIncentiveOfAchievement($role_id, $roundedAchievement)
            ?->quarter_value ?? 0.00;
    }

    /**
     * Calculates the product value component of an incentive.
     * This value is typically the product's weight multiplied by the incentive value derived from achievement.
     *
     * @param mixed $role The role object or ID (used to get role-specific incentive per achievement).
     * @param float $achievement The achievement percentage.
     * @param IncentiveMapping|null $incentive The specific incentive mapping rule.
     * @param Product $product The product for which the value is being calculated (must have 'p_w' - product weight).
     * @return float The calculated product value, rounded to two decimal places.
     */
    public function calculateProductValue(
        ?Role $role, // Role type can be mixed (e.g. model or int) as per original usage
        float $achievement,
        ?IncentiveMapping $incentive, // This specific mapping is used to check against achievement_rule
        ?float $product_weight,
    ): float {
        $incentiveValue = 0.00;
        // Check if achievement surpasses the rule of the *provided* $incentive object.
        if ($incentive && $achievement > $incentive->achievement_rule) {
            // If it does, then fetch the general incentive per achievement for the role.
            $incentiveValue = $this->getIncentivePerAchievement($achievement, $role?->id);
        }

        $productWeight = $product_weight ?? 0.0; // Ensure product weight exists
//        Log::info("Product Weight: $productWeight and Incentive Value: $incentiveValue for role $role?->id");
        return round(($productWeight * $incentiveValue) / 100, 2);
    }

    /**
     * Calculates the raw achievement percentage for a given KPI type and user.
     * This is usually fetched from stored PostVisitKpi data.
     *
     * @param KPITypes $type The KPI type.
     * @param int|null $user_id The user ID for whom the KPI percentage is fetched.
     * @return float The KPI achievement percentage, rounded to two decimal places. Defaults to 0.00.
     */
    private function calculatePostKpiPercentage(KPITypes $type, ?int $user_id = null): float
    {
        // Assuming $this has a method getPostKpi
        return round(
            $this->getPostKpi($type, $user_id)?->percent
            ?? 0.00, 2);
    }

    /**
     * Factory method to get the appropriate KPI calculation strategy based on the KPI type.
     *
     * @param KPITypes $type The KPI type.
     * @return KpiCalculationStrategy The concrete strategy instance.
     * @throws \InvalidArgumentException If the KPI type is unknown and no default strategy is defined.
     */
    private function getKpiCalculationStrategy(KPITypes $type): KpiCalculationStrategy
    {
        return match ($type) {
            KPITypes::COACHING_RATIO,
            KPITypes::COVERED_COACHING,
            KPITypes::MANAGER_COVERAGE,
            KPITypes::M_K,
            KPITypes::VACANT_RATIO => new ManagerialKpiStrategy(),
            KPITypes::COVERAGE,
            KPITypes::CALL_RATE,
            KPITypes::FREQUENCY    => new DefaultKpiStrategy(),
            // Consider adding a default case or throwing an exception for unhandled KPI types
            // default => throw new \InvalidArgumentException("Unknown KPI type: " . $type->value),
        };
    }

    /**
     * Calculates the final incentive value for a given KPI.
     * It fetches the raw KPI achievement percentage and then delegates the calculation
     * to a specific strategy based on the KPI type.
     *
     * @param KPITypes $type The KPI type.
     * @param int|null $role_id The role ID for role-specific calculations.
     * @param int|null $user_id The user ID for whom the KPI is being calculated.
     * @return float The calculated KPI incentive value.
     */
    public function calculateKpiValue(KpiTypes $type, ?int $role_id, ?int $user_id): float
    {
        $percent = $this->calculatePostKpiPercentage($type, $user_id);


        // The original code had an early return: if (empty($percent)) return 0.00;
        // This was refined to allow certain KPIs (VACANT_RATIO, M_K) to potentially process even with 0%
        // However, the strategies themselves (DefaultKpiStrategy, ManagerialKpiStrategy)
        // will typically return 0 if the percent doesn't meet criteria or if no specific incentive applies.
        // The explicit check `if (empty($percent) && ...)` was part of the previous diff and might be
        // redundant if strategies robustly handle $percent = 0.
        // For simplicity and trusting strategies to handle 0 percent correctly according to their rules:
        // if ($percent == 0.0) {
        //     // For most KPIs, 0% achievement means 0 incentive.
        //     // Specific KPIs like VACANT_RATIO might have different rules, handled by their strategy.
        // }

        $strategy = $this->getKpiCalculationStrategy($type);

        // $this refers to the instance of the class using this trait (e.g., SalesIncentiveHolder)
        // which should implement methods like getKpiIncentiveAboveMR and getKpiIncentive.
        if (!$this instanceof SalesIncentiveHolder) {
            // This check is for development time; practically, this trait is designed for SalesIncentiveHolder.
            throw new \LogicException('This trait must be used in a class that is an instance of SalesIncentiveHolder.');
        }
        $returnValue =  $strategy->calculate($type, $percent, $role_id, $this);

//        Log::info("KPI Type: $type->value, Percent: $percent, Role ID: $role_id , User ID: $user_id, Return Value: $returnValue");
        return $returnValue;
    }
}
