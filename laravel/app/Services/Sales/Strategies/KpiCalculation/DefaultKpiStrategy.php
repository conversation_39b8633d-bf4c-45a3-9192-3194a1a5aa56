<?php

namespace App\Services\Sales\Strategies\KpiCalculation;

use App\Services\Sales\SalesIncentiveHolder;
use App\Services\Enums\KPITypes;
use Illuminate\Support\Facades\Log;

/**
 * Class DefaultKpiStrategy
 *
 * Calculates KPI incentive values for standard KPIs like COVERAGE, CALL_RATE, and FREQUENCY.
 * These KPIs typically reward based on exceeding a minimum performance threshold.
 */
class DefaultKpiStrategy implements KpiCalculationStrategy
{
    /**
     * Calculates the KPI value for default types (COVERAGE, CALL_RATE, FREQUENCY).
     *
     * The calculation is based on fetching a KPI incentive schema (usually one that applies
     * "above minimum requirement") and checking if the given achievement percentage
     * surpasses the 'from_percent' defined in that schema.
     *
     * @param KPITypes $type The specific KPI type (e.g., KPITypes::COVERAGE).
     * @param float $percent The achievement percentage for this KPI.
     * @param int|null $roleId The role ID of the user/entity, used to fetch role-specific incentives.
     * @param SalesIncentiveHolder $incentiveHolder Instance to access incentive schemas.
     * @return float The calculated KPI incentive value, or 0.0 if conditions are not met or no incentive is found.
     */
    public function calculate(KPITypes $type, float $percent, ?int $roleId, SalesIncentiveHolder $incentiveHolder): float
    {
        // Logic for COVERAGE, CALL_RATE, FREQUENCY from IncentiveCalculations::calculateKpiValue
        $kpi_incentive = $incentiveHolder->getKpiIncentive($roleId, $type, $percent);


        // Ensure $kpi_incentive is not null and has the from_percent property
        if ($kpi_incentive && isset($kpi_incentive->from_percent)) {
            // Reward if the achievement percentage is greater than the minimum threshold.
            return round($percent) >= $kpi_incentive->from_percent ? ($kpi_incentive->value ?? 0.0) : 0.0;
        }

        return 0.0; // Default to 0.0 if no incentive or conditions not met.
    }
}
